import { PeerInfo } from './zeroconf-discovery';
import { SyncStatus } from '../../types/p2p-sync';
import { logZeroconfMessage } from './zeroconf-discovery';

export interface ConnectionInfo {
  peer: PeerInfo;
  status: 'connecting' | 'connected' | 'disconnected' | 'error';
  connectedAt?: Date;
  lastActivity?: Date;
  error?: string;
  syncCount: number;
  activeSyncs: string[]; // Array of database names being synced
}

export interface ConnectionManagerConfig {
  maxConcurrentConnections: number;
  connectionTimeout: number;
  healthCheckInterval: number;
  retryAttempts: number;
  retryDelay: number;
  preferredPeers: string[]; // Peer IDs to prioritize
}

export type ConnectionStatusCallback = (peerId: string, status: ConnectionInfo) => void;
export type ConnectionEventCallback = (event: 'connected' | 'disconnected' | 'error', peerId: string, info: ConnectionInfo) => void;

export class ConnectionManager {
  private config: ConnectionManagerConfig;
  private connections: Map<string, ConnectionInfo> = new Map();
  private connectionStatusCallbacks: ConnectionStatusCallback[] = [];
  private connectionEventCallbacks: ConnectionEventCallback[] = [];
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private isActive: boolean = false;

  constructor(config: Partial<ConnectionManagerConfig> = {}) {
    this.config = {
      maxConcurrentConnections: 5,
      connectionTimeout: 30000, // 30 seconds
      healthCheckInterval: 60000, // 1 minute
      retryAttempts: 3,
      retryDelay: 5000, // 5 seconds
      preferredPeers: [],
      ...config
    };
  }

  /**
   * Start the connection manager
   */
  start(): void {
    if (this.isActive) {
      return;
    }

    this.isActive = true;
    logZeroconfMessage('🔗 Connection manager started');

    // Start health check timer
    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);
  }

  /**
   * Stop the connection manager
   */
  stop(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    logZeroconfMessage('🔗 Connection manager stopped');

    // Clear health check timer
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    // Disconnect all connections
    this.disconnectAll();
  }

  /**
   * Add a peer for connection management
   */
  addPeer(peer: PeerInfo): void {
    if (this.connections.has(peer.id)) {
      logZeroconfMessage(`🔗 Peer ${peer.hostname} already managed`);
      return;
    }

    const connectionInfo: ConnectionInfo = {
      peer,
      status: 'disconnected',
      syncCount: 0,
      activeSyncs: []
    };

    this.connections.set(peer.id, connectionInfo);
    logZeroconfMessage(`🔗 Added peer ${peer.hostname} to connection manager`);

    // Auto-connect if we have capacity
    if (this.getConnectedCount() < this.config.maxConcurrentConnections) {
      this.connect(peer.id);
    }
  }

  /**
   * Remove a peer from connection management
   */
  removePeer(peerId: string): void {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) {
      return;
    }

    logZeroconfMessage(`🔗 Removing peer ${connectionInfo.peer.hostname} from connection manager`);

    // Disconnect if connected
    if (connectionInfo.status === 'connected') {
      this.disconnect(peerId);
    }

    this.connections.delete(peerId);
  }

  /**
   * Connect to a specific peer
   */
  async connect(peerId: string): Promise<boolean> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) {
      logZeroconfMessage(`🔗 ❌ Cannot connect to unknown peer ${peerId}`);
      return false;
    }

    if (connectionInfo.status === 'connected' || connectionInfo.status === 'connecting') {
      logZeroconfMessage(`🔗 Peer ${connectionInfo.peer.hostname} already connected/connecting`);
      return connectionInfo.status === 'connected';
    }

    // Check if we have reached max concurrent connections
    if (this.getConnectedCount() >= this.config.maxConcurrentConnections) {
      logZeroconfMessage(`🔗 ⚠️ Max concurrent connections reached (${this.config.maxConcurrentConnections})`);
      return false;
    }

    logZeroconfMessage(`🔗 Connecting to ${connectionInfo.peer.hostname}...`);
    
    connectionInfo.status = 'connecting';
    this.notifyStatusChange(peerId, connectionInfo);

    try {
      // Simulate connection process (in real implementation, this would establish actual connection)
      await this.establishConnection(connectionInfo.peer);
      
      connectionInfo.status = 'connected';
      connectionInfo.connectedAt = new Date();
      connectionInfo.lastActivity = new Date();
      
      logZeroconfMessage(`🔗 ✅ Connected to ${connectionInfo.peer.hostname}`);
      this.notifyStatusChange(peerId, connectionInfo);
      this.notifyEvent('connected', peerId, connectionInfo);
      
      return true;
    } catch (error) {
      connectionInfo.status = 'error';
      connectionInfo.error = (error as Error).message;
      
      logZeroconfMessage(`🔗 ❌ Failed to connect to ${connectionInfo.peer.hostname}: ${connectionInfo.error}`);
      this.notifyStatusChange(peerId, connectionInfo);
      this.notifyEvent('error', peerId, connectionInfo);
      
      return false;
    }
  }

  /**
   * Disconnect from a specific peer
   */
  async disconnect(peerId: string): Promise<void> {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) {
      return;
    }

    if (connectionInfo.status === 'disconnected') {
      return;
    }

    logZeroconfMessage(`🔗 Disconnecting from ${connectionInfo.peer.hostname}`);
    
    connectionInfo.status = 'disconnected';
    connectionInfo.activeSyncs = [];
    connectionInfo.syncCount = 0;
    
    this.notifyStatusChange(peerId, connectionInfo);
    this.notifyEvent('disconnected', peerId, connectionInfo);
  }

  /**
   * Disconnect from all peers
   */
  async disconnectAll(): Promise<void> {
    const connectedPeers = Array.from(this.connections.keys());
    
    for (const peerId of connectedPeers) {
      await this.disconnect(peerId);
    }
  }

  /**
   * Get connection info for a peer
   */
  getConnectionInfo(peerId: string): ConnectionInfo | undefined {
    return this.connections.get(peerId);
  }

  /**
   * Get all connections
   */
  getAllConnections(): Map<string, ConnectionInfo> {
    return new Map(this.connections);
  }

  /**
   * Get connected peers
   */
  getConnectedPeers(): PeerInfo[] {
    return Array.from(this.connections.values())
      .filter(info => info.status === 'connected')
      .map(info => info.peer);
  }

  /**
   * Get connected count
   */
  getConnectedCount(): number {
    return Array.from(this.connections.values())
      .filter(info => info.status === 'connected').length;
  }

  /**
   * Check if peer is connected
   */
  isConnected(peerId: string): boolean {
    const connectionInfo = this.connections.get(peerId);
    return connectionInfo?.status === 'connected' || false;
  }

  /**
   * Update sync status for a peer
   */
  updateSyncStatus(peerId: string, dbName: string, status: SyncStatus): void {
    const connectionInfo = this.connections.get(peerId);
    if (!connectionInfo) {
      return;
    }

    connectionInfo.lastActivity = new Date();

    if (status.status === 'active') {
      if (!connectionInfo.activeSyncs.includes(dbName)) {
        connectionInfo.activeSyncs.push(dbName);
        connectionInfo.syncCount++;
      }
    } else if (status.status === 'complete' || status.status === 'error') {
      const index = connectionInfo.activeSyncs.indexOf(dbName);
      if (index > -1) {
        connectionInfo.activeSyncs.splice(index, 1);
      }
    }

    this.notifyStatusChange(peerId, connectionInfo);
  }

  /**
   * Get connection summary
   */
  getConnectionSummary(): {
    total: number;
    connected: number;
    connecting: number;
    disconnected: number;
    error: number;
    activeSyncs: number;
  } {
    const connections = Array.from(this.connections.values());
    const summary = {
      total: connections.length,
      connected: 0,
      connecting: 0,
      disconnected: 0,
      error: 0,
      activeSyncs: 0
    };

    connections.forEach(info => {
      switch (info.status) {
        case 'connected':
          summary.connected++;
          break;
        case 'connecting':
          summary.connecting++;
          break;
        case 'disconnected':
          summary.disconnected++;
          break;
        case 'error':
          summary.error++;
          break;
      }
      summary.activeSyncs += info.activeSyncs.length;
    });

    return summary;
  }

  /**
   * Subscribe to connection status changes
   */
  onConnectionStatusChange(callback: ConnectionStatusCallback): void {
    this.connectionStatusCallbacks.push(callback);
  }

  /**
   * Subscribe to connection events
   */
  onConnectionEvent(callback: ConnectionEventCallback): void {
    this.connectionEventCallbacks.push(callback);
  }

  /**
   * Perform health check on all connections
   */
  private performHealthCheck(): void {
    if (!this.isActive) {
      return;
    }

    logZeroconfMessage('🔗 🏥 Performing connection health check');

    const now = new Date();
    const connections = Array.from(this.connections.values());

    connections.forEach(info => {
      if (info.status === 'connected' && info.lastActivity) {
        const timeSinceActivity = now.getTime() - info.lastActivity.getTime();
        
        // If no activity for 5 minutes, consider connection stale
        if (timeSinceActivity > 300000) {
          logZeroconfMessage(`🔗 ⚠️ Connection to ${info.peer.hostname} appears stale`);
          
          // In real implementation, would ping the peer or check sync status
          // For now, just update last activity
          info.lastActivity = now;
        }
      }
    });
  }

  /**
   * Establish connection to a peer (simulation)
   */
  private async establishConnection(peer: PeerInfo): Promise<void> {
    // In real implementation, this would:
    // 1. Test network connectivity to peer
    // 2. Verify peer is still available
    // 3. Perform any authentication if needed
    // 4. Set up any connection pooling
    
    // For now, simulate connection with a delay
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // Simulate occasional connection failures
    if (Math.random() < 0.05) { // 5% failure rate
      throw new Error('Connection failed');
    }
  }

  /**
   * Notify status change callbacks
   */
  private notifyStatusChange(peerId: string, info: ConnectionInfo): void {
    this.connectionStatusCallbacks.forEach(callback => {
      try {
        callback(peerId, info);
      } catch (error) {
        console.error('Error in connection status callback:', error);
      }
    });
  }

  /**
   * Notify event callbacks
   */
  private notifyEvent(event: 'connected' | 'disconnected' | 'error', peerId: string, info: ConnectionInfo): void {
    this.connectionEventCallbacks.forEach(callback => {
      try {
        callback(event, peerId, info);
      } catch (error) {
        console.error('Error in connection event callback:', error);
      }
    });
  }
}