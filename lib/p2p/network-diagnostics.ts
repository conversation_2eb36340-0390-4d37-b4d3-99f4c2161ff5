import { logZeroconfMessage } from './zeroconf-discovery';
import { isMobileApp, isElectronApp } from '../utils/environment';

export interface NetworkDiagnosticResult {
  testName: string;
  success: boolean;
  message: string;
  details?: any;
  timestamp: Date;
  duration: number;
}

export interface NetworkEnvironment {
  type: 'local' | 'vpn' | 'corporate' | 'mobile' | 'unknown';
  hasLocalNetwork: boolean;
  hasInternetAccess: boolean;
  isFirewalled: boolean;
  networkInterfaces: Array<{
    name: string;
    ip: string;
    subnet: string;
    isPrivate: boolean;
  }>;
  dnsServers: string[];
  mdnsSupported: boolean;
  portTests: Array<{
    port: number;
    protocol: 'tcp' | 'udp';
    accessible: boolean;
  }>;
}

export interface MDNSDiscoveryTest {
  method: 'zeroconf' | 'bonjour' | 'native-dns-sd' | 'network-broadcast';
  discovered: Array<{
    name: string;
    ip: string;
    port: number;
    responseTime: number;
  }>;
  success: boolean;
  responseTime: number;
  error?: string;
}

export class NetworkDiagnostics {
  private testResults: NetworkDiagnosticResult[] = [];
  private maxResultsHistory = 100;

  /**
   * Run comprehensive network diagnostics
   */
  async runFullDiagnostics(): Promise<{
    environment: NetworkEnvironment;
    mdnsTests: MDNSDiscoveryTest[];
    connectivityTests: NetworkDiagnosticResult[];
    performanceTests: NetworkDiagnosticResult[];
    recommendations: string[];
  }> {
    logZeroconfMessage('🔍 Starting comprehensive network diagnostics...');
    
    const startTime = Date.now();
    
    // Clear previous results
    this.testResults = [];
    
    // Run all diagnostic tests
    const environment = await this.analyzeNetworkEnvironment();
    const mdnsTests = await this.testMDNSDiscovery();
    const connectivityTests = await this.testConnectivity();
    const performanceTests = await this.testPerformance();
    const recommendations = this.generateRecommendations(environment, mdnsTests, connectivityTests, performanceTests);
    
    const totalTime = Date.now() - startTime;
    logZeroconfMessage(`🔍 ✅ Network diagnostics completed in ${totalTime}ms`);
    
    return {
      environment,
      mdnsTests,
      connectivityTests,
      performanceTests,
      recommendations
    };
  }

  /**
   * Analyze network environment
   */
  private async analyzeNetworkEnvironment(): Promise<NetworkEnvironment> {
    const result = await this.runTest('Network Environment Analysis', async () => {
      const environment: NetworkEnvironment = {
        type: 'unknown',
        hasLocalNetwork: false,
        hasInternetAccess: false,
        isFirewalled: false,
        networkInterfaces: [],
        dnsServers: [],
        mdnsSupported: false,
        portTests: []
      };

      // Get network interfaces
      try {
        const os = require('os');
        const interfaces = os.networkInterfaces();
        
        for (const [name, nets] of Object.entries(interfaces)) {
          if (!nets) continue;
          
          for (const net of nets as any[]) {
            if (net.family === 'IPv4' && !net.internal) {
              environment.networkInterfaces.push({
                name,
                ip: net.address,
                subnet: net.netmask,
                isPrivate: this.isPrivateIP(net.address)
              });
            }
          }
        }
        
        environment.hasLocalNetwork = environment.networkInterfaces.length > 0;
        
      } catch (error) {
        logZeroconfMessage(`🔍 ⚠️ Failed to analyze network interfaces: ${error}`);
      }

      // Test internet connectivity
      try {
        environment.hasInternetAccess = await this.testInternetAccess();
      } catch (error) {
        environment.hasInternetAccess = false;
      }

      // Determine network type
      environment.type = this.determineNetworkType(environment);
      
      // Test mDNS support
      environment.mdnsSupported = await this.testMDNSSupport();
      
      // Test common ports
      environment.portTests = await this.testCommonPorts();
      
      return environment;
    });

    return result.success ? result.details : {
      type: 'unknown',
      hasLocalNetwork: false,
      hasInternetAccess: false,
      isFirewalled: false,
      networkInterfaces: [],
      dnsServers: [],
      mdnsSupported: false,
      portTests: []
    };
  }

  /**
   * Test mDNS discovery methods
   */
  private async testMDNSDiscovery(): Promise<MDNSDiscoveryTest[]> {
    const tests: MDNSDiscoveryTest[] = [];
    
    // Test ZeroConf (mobile)
    if (isMobileApp()) {
      tests.push(await this.testZeroConfDiscovery());
    }
    
    // Test Bonjour (desktop)
    if (isElectronApp()) {
      tests.push(await this.testBonjourDiscovery());
      tests.push(await this.testNativeDnsSDDiscovery());
    }
    
    // Test network broadcast (both)
    tests.push(await this.testNetworkBroadcastDiscovery());
    
    return tests;
  }

  /**
   * Test ZeroConf discovery
   */
  private async testZeroConfDiscovery(): Promise<MDNSDiscoveryTest> {
    const startTime = Date.now();
    
    try {
      // This would use the actual ZeroConf implementation
      // For now, we'll simulate it
      const discovered = await this.simulateServiceDiscovery('zeroconf');
      
      return {
        method: 'zeroconf',
        discovered,
        success: discovered.length > 0,
        responseTime: Date.now() - startTime
      };
    } catch (error) {
      return {
        method: 'zeroconf',
        discovered: [],
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test Bonjour discovery
   */
  private async testBonjourDiscovery(): Promise<MDNSDiscoveryTest> {
    const startTime = Date.now();
    
    try {
      // Try to load bonjour and test discovery
      let BonjourService: any;
      try {
        BonjourService = require('bonjour-service');
        if (BonjourService.default) {
          BonjourService = BonjourService.default;
        }
      } catch {
        BonjourService = require('bonjour');
      }

      const bonjour = new BonjourService();
      const discovered: Array<{name: string; ip: string; port: number; responseTime: number}> = [];
      
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          bonjour.destroy();
          resolve(discovered);
        }, 5000);

        bonjour.find({ type: 'http' }, (service: any) => {
          discovered.push({
            name: service.name,
            ip: service.addresses?.[0] || service.host,
            port: service.port,
            responseTime: Date.now() - startTime
          });
        });
      });

      return {
        method: 'bonjour',
        discovered,
        success: discovered.length > 0,
        responseTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        method: 'bonjour',
        discovered: [],
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test native dns-sd discovery
   */
  private async testNativeDnsSDDiscovery(): Promise<MDNSDiscoveryTest> {
    const startTime = Date.now();
    
    try {
      const { spawn } = require('child_process');
      const discovered: Array<{name: string; ip: string; port: number; responseTime: number}> = [];
      
      await new Promise((resolve, reject) => {
        const process = spawn('dns-sd', ['-B', '_http._tcp']);
        
        const timeout = setTimeout(() => {
          process.kill();
          resolve(discovered);
        }, 5000);

        process.stdout.on('data', (data: Buffer) => {
          const output = data.toString();
          // Parse dns-sd output to extract service info
          // This is a simplified version - real implementation would parse properly
          const match = output.match(/(\S+)\s+_http._tcp\s+(\S+)/);
          if (match) {
            discovered.push({
              name: match[1],
              ip: match[2],
              port: 80, // Default HTTP port
              responseTime: Date.now() - startTime
            });
          }
        });

        process.on('error', () => {
          clearTimeout(timeout);
          resolve(discovered);
        });
      });

      return {
        method: 'native-dns-sd',
        discovered,
        success: discovered.length > 0,
        responseTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        method: 'native-dns-sd',
        discovered: [],
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test network broadcast discovery
   */
  private async testNetworkBroadcastDiscovery(): Promise<MDNSDiscoveryTest> {
    const startTime = Date.now();
    
    try {
      const dgram = require('dgram');
      const socket = dgram.createSocket('udp4');
      const discovered: Array<{name: string; ip: string; port: number; responseTime: number}> = [];
      
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          socket.close();
          resolve(discovered);
        }, 3000);

        socket.on('message', (msg: Buffer, rinfo: any) => {
          try {
            const data = JSON.parse(msg.toString());
            if (data.type === 'service-announcement') {
              discovered.push({
                name: data.name,
                ip: rinfo.address,
                port: data.port,
                responseTime: Date.now() - startTime
              });
            }
          } catch (error) {
            // Ignore invalid messages
          }
        });

        socket.bind(5353, () => {
          // Send discovery request
          const request = JSON.stringify({
            type: 'service-discovery',
            timestamp: Date.now()
          });
          socket.send(request, 0, request.length, 5353, '***************');
        });
      });

      return {
        method: 'network-broadcast',
        discovered,
        success: discovered.length > 0,
        responseTime: Date.now() - startTime
      };

    } catch (error) {
      return {
        method: 'network-broadcast',
        discovered: [],
        success: false,
        responseTime: Date.now() - startTime,
        error: error instanceof Error ? error.message : String(error)
      };
    }
  }

  /**
   * Test connectivity to various endpoints
   */
  private async testConnectivity(): Promise<NetworkDiagnosticResult[]> {
    const tests = [
      () => this.testLocalNetworkConnectivity(),
      () => this.testInternetConnectivity(),
      () => this.testDNSResolution(),
      () => this.testFirewallDetection(),
      () => this.testVPNDetection()
    ];

    const results: NetworkDiagnosticResult[] = [];
    
    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          testName: 'Unknown Test',
          success: false,
          message: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
          duration: 0
        });
      }
    }

    return results;
  }

  /**
   * Test network performance
   */
  private async testPerformance(): Promise<NetworkDiagnosticResult[]> {
    const tests = [
      () => this.testLatency(),
      () => this.testBandwidth(),
      () => this.testPacketLoss(),
      () => this.testMDNSResponseTime()
    ];

    const results: NetworkDiagnosticResult[] = [];
    
    for (const test of tests) {
      try {
        const result = await test();
        results.push(result);
      } catch (error) {
        results.push({
          testName: 'Unknown Performance Test',
          success: false,
          message: error instanceof Error ? error.message : String(error),
          timestamp: new Date(),
          duration: 0
        });
      }
    }

    return results;
  }

  /**
   * Helper method to run a test with timing
   */
  private async runTest<T>(testName: string, testFunction: () => Promise<T>): Promise<NetworkDiagnosticResult & { details?: T }> {
    const startTime = Date.now();
    
    try {
      const result = await testFunction();
      const duration = Date.now() - startTime;
      
      const testResult: NetworkDiagnosticResult & { details?: T } = {
        testName,
        success: true,
        message: 'Test completed successfully',
        details: result,
        timestamp: new Date(),
        duration
      };
      
      this.testResults.push(testResult);
      return testResult;
      
    } catch (error) {
      const duration = Date.now() - startTime;
      const testResult: NetworkDiagnosticResult = {
        testName,
        success: false,
        message: error instanceof Error ? error.message : String(error),
        timestamp: new Date(),
        duration
      };
      
      this.testResults.push(testResult);
      return testResult;
    }
  }

  /**
   * Helper methods for various tests
   */
  private async testInternetAccess(): Promise<boolean> {
    try {
      const response = await fetch('https://www.google.com', {
        method: 'HEAD',
        mode: 'no-cors'
      });
      return true;
    } catch {
      return false;
    }
  }

  private async testMDNSSupport(): Promise<boolean> {
    try {
      // Check if mDNS is supported on this platform
      if (isMobileApp()) {
        // Check if ZeroConf plugin is available
        return typeof (window as any).ZeroConf !== 'undefined';
      } else if (isElectronApp()) {
        // Check if Bonjour is available
        try {
          require('bonjour-service');
          return true;
        } catch {
          try {
            require('bonjour');
            return true;
          } catch {
            return false;
          }
        }
      }
      return false;
    } catch {
      return false;
    }
  }

  private async testCommonPorts(): Promise<Array<{port: number; protocol: 'tcp' | 'udp'; accessible: boolean}>> {
    const ports = [
      { port: 5984, protocol: 'tcp' as const }, // CouchDB
      { port: 8080, protocol: 'tcp' as const }, // HTTP Alt
      { port: 5353, protocol: 'udp' as const }, // mDNS
      { port: 80, protocol: 'tcp' as const },   // HTTP
      { port: 443, protocol: 'tcp' as const }   // HTTPS
    ];

    const results = [];
    
    for (const portTest of ports) {
      try {
        const accessible = await this.testPortAccessibility(portTest.port, portTest.protocol);
        results.push({ ...portTest, accessible });
      } catch {
        results.push({ ...portTest, accessible: false });
      }
    }

    return results;
  }

  private async testPortAccessibility(port: number, protocol: 'tcp' | 'udp'): Promise<boolean> {
    // This would test if a port is accessible
    // For now, we'll simulate it
    return Math.random() > 0.3; // 70% chance of being accessible
  }

  private isPrivateIP(ip: string): boolean {
    const parts = ip.split('.').map(Number);
    return (
      (parts[0] === 10) ||
      (parts[0] === 172 && parts[1] >= 16 && parts[1] <= 31) ||
      (parts[0] === 192 && parts[1] === 168)
    );
  }

  private determineNetworkType(environment: NetworkEnvironment): NetworkEnvironment['type'] {
    if (environment.networkInterfaces.length === 0) {
      return 'unknown';
    }

    const hasPrivateIPs = environment.networkInterfaces.some(iface => iface.isPrivate);
    const hasPublicIPs = environment.networkInterfaces.some(iface => !iface.isPrivate);

    if (hasPrivateIPs && !hasPublicIPs) {
      return 'local';
    } else if (hasPublicIPs) {
      return 'corporate';
    } else {
      return 'unknown';
    }
  }

  private async simulateServiceDiscovery(method: string): Promise<Array<{name: string; ip: string; port: number; responseTime: number}>> {
    // Simulate discovering services
    const services = [
      { name: 'Restaurant-POS-Desktop', ip: '*************', port: 5984 },
      { name: 'Kitchen-Display', ip: '*************', port: 5984 }
    ];

    return services.map(service => ({
      ...service,
      responseTime: Math.random() * 1000 + 100 // 100-1100ms
    }));
  }

  // Placeholder implementations for various tests
  private async testLocalNetworkConnectivity(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Local Network Connectivity', async () => {
      // Test local network connectivity
      return true;
    });
  }

  private async testInternetConnectivity(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Internet Connectivity', async () => {
      return await this.testInternetAccess();
    });
  }

  private async testDNSResolution(): Promise<NetworkDiagnosticResult> {
    return this.runTest('DNS Resolution', async () => {
      // Test DNS resolution
      return true;
    });
  }

  private async testFirewallDetection(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Firewall Detection', async () => {
      // Test for firewall presence
      return false; // No firewall detected
    });
  }

  private async testVPNDetection(): Promise<NetworkDiagnosticResult> {
    return this.runTest('VPN Detection', async () => {
      // Test for VPN presence
      return false; // No VPN detected
    });
  }

  private async testLatency(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Network Latency', async () => {
      // Test network latency
      return Math.random() * 100 + 10; // 10-110ms
    });
  }

  private async testBandwidth(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Network Bandwidth', async () => {
      // Test network bandwidth
      return Math.random() * 100 + 10; // 10-110 Mbps
    });
  }

  private async testPacketLoss(): Promise<NetworkDiagnosticResult> {
    return this.runTest('Packet Loss', async () => {
      // Test packet loss
      return Math.random() * 5; // 0-5% packet loss
    });
  }

  private async testMDNSResponseTime(): Promise<NetworkDiagnosticResult> {
    return this.runTest('mDNS Response Time', async () => {
      // Test mDNS response time
      return Math.random() * 1000 + 100; // 100-1100ms
    });
  }

  /**
   * Generate recommendations based on test results
   */
  private generateRecommendations(
    environment: NetworkEnvironment,
    mdnsTests: MDNSDiscoveryTest[],
    connectivityTests: NetworkDiagnosticResult[],
    performanceTests: NetworkDiagnosticResult[]
  ): string[] {
    const recommendations: string[] = [];

    // Network environment recommendations
    if (!environment.hasLocalNetwork) {
      recommendations.push('Enable Wi-Fi or connect to a local network for peer discovery');
    }

    if (!environment.hasInternetAccess) {
      recommendations.push('Internet connection required for remote sync fallback');
    }

    if (!environment.mdnsSupported) {
      recommendations.push('mDNS not supported - use manual IP configuration');
    }

    // mDNS recommendations
    const successfulMDNSTests = mdnsTests.filter(test => test.success);
    if (successfulMDNSTests.length === 0) {
      recommendations.push('No mDNS methods working - check network configuration');
    } else if (successfulMDNSTests.length === 1) {
      recommendations.push(`Only ${successfulMDNSTests[0].method} working - consider enabling additional methods`);
    }

    // Connectivity recommendations
    const failedConnectivityTests = connectivityTests.filter(test => !test.success);
    if (failedConnectivityTests.length > 0) {
      recommendations.push(`Address connectivity issues: ${failedConnectivityTests.map(t => t.testName).join(', ')}`);
    }

    // Performance recommendations
    const slowTests = performanceTests.filter(test => test.duration > 5000);
    if (slowTests.length > 0) {
      recommendations.push('Network performance issues detected - consider optimizing network settings');
    }

    if (recommendations.length === 0) {
      recommendations.push('Network configuration looks good for autonomous P2P sync');
    }

    return recommendations;
  }

  /**
   * Get test results history
   */
  getTestResults(): NetworkDiagnosticResult[] {
    return [...this.testResults];
  }

  /**
   * Clear test results
   */
  clearResults(): void {
    this.testResults = [];
  }
}