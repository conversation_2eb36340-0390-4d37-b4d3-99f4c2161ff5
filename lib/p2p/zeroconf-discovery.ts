import { registerPlugin } from '@capacitor/core';
import { v4 as uuidv4 } from 'uuid';
import { isMobileApp, isElectronApp } from '../utils/environment';

// Define the ZeroConf plugin interface
interface ZeroConfPlugin {
  getHostname(): Promise<{ hostname: string }>;
  watch(options: { type: string; domain: string }): Promise<void>;
  unwatch(options: { type: string; domain: string }): Promise<void>;
  close(): Promise<void>;
  addListener(eventName: 'discover', listenerFunc: (result: ZeroConfWatchResult) => void): Promise<any>;
}

// Define the ZeroConf watch result interface
interface ZeroConfWatchResult {
  action: 'added' | 'removed' | 'resolved';
  service: {
    domain: string;
    type: string;
    name: string;
    port: number;
    hostname: string;
    ipv4Addresses: string[];
    ipv6Addresses: string[];
    txtRecord: Record<string, string>;
  };
}

let Zeroconf: ReturnType<typeof registerPlugin<ZeroConfPlugin>> | null = null;
function getZeroconfInstance() {
  if (!Zeroconf) {
    Zeroconf = registerPlugin<ZeroConfPlugin>('ZeroConf');
  }
  return Zeroconf;
}

// Service type for discovery
const SERVICE_TYPE = '_http._tcp.';
const SERVICE_DOMAIN = 'local.';

// Global list of zeroconf log subscribers
const zeroconfLogSubscribers: Array<(log: string) => void> = [];

/**
 * Subscribe to Zeroconf logs
 * @param callback Function to call with log message
 */
export function onZeroconfLog(callback: (log: string) => void): void {
  zeroconfLogSubscribers.push(callback);
}

/**
 * Log a message to all Zeroconf log subscribers
 * @param message 
 */
export function logZeroconfMessage(message: string): void {
  const timestamp = new Date().toISOString();
  const formattedMessage = `${timestamp} - ${message}`;
  console.log(`[ZeroConf] ${message}`);
  zeroconfLogSubscribers.forEach(subscriber => subscriber(formattedMessage));
}

/**
 * MOBILE CLIENT ONLY - ZeroConf Discovery Service
 * 
 * This class is designed exclusively for mobile devices to discover desktop hubs.
 * Mobile devices do not publish services since they don't host HTTP servers.
 */
export class ZeroConfDiscovery {
  private deviceId: string;
  private isInitialized: boolean = false;
  private peers: Map<string, PeerInfo> = new Map();
  private onPeerDiscoveredCallbacks: ((peer: PeerInfo) => void)[] = [];
  private onPeerLostCallbacks: ((peerId: string) => void)[] = [];
  private mdnsStatus: 'not_running' | 'running' | 'error' = 'not_running';
  
  // Discovery timeout support
  private discoveryTimeout: number = 60000; // 60 seconds default
  private discoveryTimer: NodeJS.Timeout | null = null;
  private onDiscoveryTimeoutCallbacks: (() => void)[] = [];
  
  // Auto-connection support
  private autoConnectEnabled: boolean = false;
  private autoConnectCallback: ((peer: PeerInfo) => void) | null = null;

  constructor(deviceId?: string) {
    this.deviceId = deviceId || uuidv4();
    console.log(`[ZeroConfDiscovery] Created mobile client with device ID: ${this.deviceId}`);
  }

  /**
   * Get the current mDNS status
   */
  getMdnsStatus(): 'not_running' | 'running' | 'error' {
    return this.mdnsStatus;
  }

  /**
   * Initialize the ZeroConf discovery service for mobile clients only
   */
  async initialize(port: number): Promise<boolean> {
    if (!isMobileApp()) {
      this.mdnsStatus = 'not_running';
      console.log('[ZeroConfDiscovery] Not a mobile app, skipping initialization');
      return false;
    }

    if (this.isInitialized) {
      this.mdnsStatus = 'running';
      console.log('[ZeroConfDiscovery] Already initialized');
      return true;
    }

    try {
      console.log(`[ZeroConfDiscovery] Initializing mobile client discovery`);
      const zeroconf = getZeroconfInstance();
      if (!zeroconf) {
        this.mdnsStatus = 'error';
        throw new Error('ZeroConf plugin not available');
      }

      logZeroconfMessage(`📱 ZeroConf mobile client initialized`);
      
      try {
        await this.setupDiscoveryListener();
        console.log('[ZeroConfDiscovery] Discovery listener set up successfully');
      } catch (listenerError) {
        this.mdnsStatus = 'error';
        console.error('[ZeroConfDiscovery] Error setting up discovery listener:', listenerError);
        throw listenerError;
      }
      
      this.isInitialized = true;
      this.mdnsStatus = 'running';
      logZeroconfMessage(`✅ Mobile client discovery ready - listening for desktop hubs`);
      return true;
    } catch (error) {
      this.mdnsStatus = 'error';
      console.error('[ZeroConfDiscovery] Initialization error:', error);
      logZeroconfMessage(`❌ Failed to initialize mobile discovery: ${error}`);
      return false;
    }
  }

  /**
   * Set up the discovery listener for mobile clients
   */
  private async setupDiscoveryListener(): Promise<void> {
    console.log('[ZeroConfDiscovery] Setting up mobile discovery listener');
    logZeroconfMessage('👀 mDNS: Mobile listening for desktop hubs...');
    this.mdnsStatus = 'running';
    
    const zeroconf = getZeroconfInstance();
    if (!zeroconf) {
      this.mdnsStatus = 'error';
      throw new Error('ZeroConf plugin not available');
    }
    
    try {
      await zeroconf.addListener('discover', (result: ZeroConfWatchResult) => {
        const { action, service } = result;
        
        // Detailed debug logging of the entire service object
        try {
          const safeServiceForLog = {
            ...service,
            ipv4Addresses: service.ipv4Addresses || [],
            ipv6Addresses: service.ipv6Addresses || [],
            txtRecord: service.txtRecord || {}
          };
          logZeroconfMessage(`🔍 Raw mDNS service data: ${JSON.stringify(safeServiceForLog)}`);
        } catch (error) {
          console.error('Error logging service object:', error);
        }
        
        // Skip our own service (shouldn't happen since mobile doesn't publish)
        if (service.txtRecord && service.txtRecord.id === this.deviceId) {
          logZeroconfMessage('🔎 mDNS: Skipping our own service (this should not happen on mobile)');
          return;
        }
        
        // Log raw mDNS events for debugging
        logZeroconfMessage(`👂 mDNS event: action=${action}, service=${service.name}`);
        const hasAddress = (service.ipv4Addresses?.length > 0 || service.ipv6Addresses?.length > 0);
        
        // Handle service additions or resolutions with a valid address
        if ((action === 'added' || action === 'resolved') && hasAddress) {
          logZeroconfMessage(`🔍 Discovered service: ${service.name}`);
          const peerId = service.txtRecord?.id || '';
          if (!peerId) {
            logZeroconfMessage('⚠️ Discovered service without an ID, skipping');
            return;
          }
          
          // Only accept desktop services (filter out any mobile services that might be discovered)
          const platform = service.txtRecord?.platform || 'unknown';
          if (platform !== 'desktop' && !service.name.toLowerCase().includes('desktop') && !service.name.toLowerCase().includes('electron')) {
            logZeroconfMessage(`⚠️ Skipping non-desktop service: ${service.name} (platform: ${platform})`);
            return;
          }
          
          const peerInfo: PeerInfo = {
            id: peerId,
            ip: service.ipv4Addresses?.[0] || service.ipv6Addresses?.[0] || '',
            port: service.port,
            hostname: service.name,
            platform: 'desktop' // Force to desktop since we only accept desktop services
          };
          
          this.peers.set(peerInfo.id, peerInfo);
          logZeroconfMessage(`✅ Desktop hub discovered: ${peerInfo.hostname} (${peerInfo.ip}:${peerInfo.port})`);
          
          // Cancel discovery timeout when first peer is found
          if (this.peers.size === 1 && this.discoveryTimer) {
            this.cancelDiscoveryTimeout();
            logZeroconfMessage('🎯 First peer found - cancelling discovery timeout');
          }
          
          // Trigger auto-connection if enabled
          if (this.autoConnectEnabled && this.autoConnectCallback) {
            logZeroconfMessage(`🔗 Auto-connecting to ${peerInfo.hostname}`);
            this.autoConnectCallback(peerInfo);
          }
          
          this.onPeerDiscoveredCallbacks.forEach(callback => callback(peerInfo));
        } else if (action === 'removed') {
          const peerId = service.txtRecord?.id || '';
          if (!peerId) return;
          logZeroconfMessage(`👋 Lost desktop hub: ${service.name}`);
          this.peers.delete(peerId);
          this.onPeerLostCallbacks.forEach(callback => callback(peerId));
        }
      });
      console.log('[ZeroConfDiscovery] Discovery listener set up successfully');
    } catch (error) {
      this.mdnsStatus = 'error';
      console.error('[ZeroConfDiscovery] Error setting up discovery listener:', error);
      logZeroconfMessage(`⚠️ Warning: Error setting up discovery listener: ${error}`);
    }
  }

  /**
   * Start watching for desktop hub services
   */
  async startWatching(): Promise<boolean> {
    if (!this.isInitialized) {
      logZeroconfMessage('❌ Cannot start watching - not initialized');
      return false;
    }

    try {
      const zeroconf = getZeroconfInstance();
      if (!zeroconf) {
        this.mdnsStatus = 'error';
        throw new Error('ZeroConf plugin not available');
      }

      logZeroconfMessage('🔍 Starting to watch for desktop hub services...');
      
      await zeroconf.watch({
        type: SERVICE_TYPE,
        domain: SERVICE_DOMAIN
      });
      
      this.mdnsStatus = 'running';
      logZeroconfMessage('✅ Mobile client is now watching for desktop hubs');
      return true;
    } catch (error) {
      this.mdnsStatus = 'error';
      console.error('[ZeroConfDiscovery] Error starting to watch:', error);
      logZeroconfMessage(`❌ Failed to start watching: ${error}`);
      return false;
    }
  }

  getPeers(): PeerInfo[] {
    return Array.from(this.peers.values()).filter(peer => peer.platform === 'desktop');
  }

  onPeerDiscovered(callback: (peer: PeerInfo) => void): void {
    this.onPeerDiscoveredCallbacks.push(callback);
  }

  onPeerLost(callback: (peerId: string) => void): void {
    this.onPeerLostCallbacks.push(callback);
  }

  async stopWatching(): Promise<void> {
    try {
      const zeroconf = getZeroconfInstance();
      if (zeroconf) {
        await zeroconf.unwatch({
          type: SERVICE_TYPE,
          domain: SERVICE_DOMAIN
        });
        logZeroconfMessage('📴 Stopped watching for services');
      }
    } catch (error) {
      console.error('[ZeroConfDiscovery] Error stopping watch:', error);
      logZeroconfMessage(`⚠️ Error stopping watch: ${error}`);
    }
  }

  async close(): Promise<void> {
    try {
      await this.stopWatching();
      
      // Clear discovery timer
      if (this.discoveryTimer) {
        clearTimeout(this.discoveryTimer);
        this.discoveryTimer = null;
      }
      
      const zeroconf = getZeroconfInstance();
      if (zeroconf) {
        await zeroconf.close();
      }
      this.peers.clear();
      this.isInitialized = false;
      this.mdnsStatus = 'not_running';
      logZeroconfMessage('📴 Mobile discovery service closed');
    } catch (error) {
      console.error('[ZeroConfDiscovery] Error closing:', error);
      logZeroconfMessage(`⚠️ Error closing discovery service: ${error}`);
    }
  }

  /**
   * Configure discovery timeout
   */
  setDiscoveryTimeout(timeout: number): void {
    this.discoveryTimeout = timeout;
    logZeroconfMessage(`⏱️ Discovery timeout set to ${timeout}ms`);
  }

  /**
   * Start discovery with timeout
   */
  async startDiscoveryWithTimeout(timeout?: number): Promise<boolean> {
    if (timeout) {
      this.discoveryTimeout = timeout;
    }

    const started = await this.startWatching();
    if (!started) {
      return false;
    }

    // Set timeout for discovery
    this.discoveryTimer = setTimeout(() => {
      logZeroconfMessage(`⏰ Discovery timeout reached (${this.discoveryTimeout}ms)`);
      this.onDiscoveryTimeoutCallbacks.forEach(callback => callback());
    }, this.discoveryTimeout);

    logZeroconfMessage(`🔍 Started discovery with ${this.discoveryTimeout}ms timeout`);
    return true;
  }

  /**
   * Cancel discovery timeout
   */
  cancelDiscoveryTimeout(): void {
    if (this.discoveryTimer) {
      clearTimeout(this.discoveryTimer);
      this.discoveryTimer = null;
      logZeroconfMessage('⏰ Discovery timeout cancelled');
    }
  }

  /**
   * Subscribe to discovery timeout events
   */
  onDiscoveryTimeout(callback: () => void): void {
    this.onDiscoveryTimeoutCallbacks.push(callback);
  }

  /**
   * Enable auto-connection for newly discovered peers
   */
  enableAutoConnect(callback: (peer: PeerInfo) => void): void {
    this.autoConnectEnabled = true;
    this.autoConnectCallback = callback;
    logZeroconfMessage('🔗 Auto-connection enabled');
  }

  /**
   * Disable auto-connection
   */
  disableAutoConnect(): void {
    this.autoConnectEnabled = false;
    this.autoConnectCallback = null;
    logZeroconfMessage('🔗 Auto-connection disabled');
  }

  /**
   * Check if auto-connection is enabled
   */
  isAutoConnectEnabled(): boolean {
    return this.autoConnectEnabled;
  }

  /**
   * Get discovered peers count
   */
  getPeerCount(): number {
    return this.peers.size;
  }

  /**
   * Check if discovery is active (timeout not reached)
   */
  isDiscoveryActive(): boolean {
    return this.discoveryTimer !== null;
  }

  /**
   * Get time remaining for discovery timeout
   */
  getDiscoveryTimeRemaining(): number {
    if (!this.discoveryTimer) {
      return 0;
    }
    // Note: This is an approximation since we don't track start time
    return this.discoveryTimeout;
  }
}

export interface PeerInfo {
  id: string;
  ip: string;
  port: number;
  hostname: string;
  platform: string;
} 