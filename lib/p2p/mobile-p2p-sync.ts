import { isMobileApp } from '../utils/environment';
import { ZeroConfDiscovery, PeerInfo } from './zeroconf-discovery';
import { v4 as uuidv4 } from 'uuid';
import { SyncStatus } from '../../types/p2p-sync';
import { logZeroconfMessage } from './zeroconf-discovery';
import { validateRestaurantContext, validateDatabaseName, lockRestaurantContext, validateRestaurantLock } from '../sync/restaurant-validation';

// Internet sync configuration
interface InternetSyncConfig {
  enabled: boolean;
  serverUrl: string;
  authToken: string | null;
  fallbackDelay: number;
}

// Internet peer info
interface InternetPeerInfo extends PeerInfo {
  isInternetPeer: boolean;
  proxyUrl?: string;
}

// Type definitions for event callbacks
type PeerDiscoveredCallback = (peer: PeerInfo) => void;
type PeerLostCallback = (peerId: string) => void;
type SyncStatusUpdatedCallback = (status: SyncStatus) => void;

// Multi-master sync configuration
interface MultiMasterConfig {
  enabled: boolean;
  databases: string[];
  autoSync: boolean;
  direction: 'push' | 'pull' | 'both';
}

/**
 * Mobile P2P Sync Manager - CLIENT ONLY with Multi-Master Support
 * 
 * This class manages P2P sync for mobile devices (Android/iOS) as clients that
 * discover and connect to desktop hubs. Mobile devices:
 * - Do NOT publish services (no HTTP server)
 * - Do NOT discover other mobile devices
 * - Only discover desktop hubs via ZeroConf/mDNS
 * - Can sync with MULTIPLE desktop hubs simultaneously (multi-master)
 * - Always act as client (desktop is always the server/hub)
 */
export class MobileP2PSync {
  private deviceId: string;
  private discovery: ZeroConfDiscovery;
  private isInitialized: boolean = false;
  private syncStatusList: SyncStatus[] = [];
  private activeSyncs: Map<string, any> = new Map();
  private pouchDbInstance: any | null = null;
  
  // Multi-master sync configuration
  private multiMasterConfig: MultiMasterConfig = {
    enabled: false,
    databases: [],
    autoSync: true,
    direction: 'both'
  };
  
  // Track which databases are configured for multi-master
  private multiMasterDatabases: Set<string> = new Set();
  
  // Internet sync configuration
  private internetSyncConfig: InternetSyncConfig = {
    enabled: false,
    serverUrl: '',
    authToken: null,
    fallbackDelay: 5000
  };
  
  // Track internet peers separately
  private internetPeers: Map<string, InternetPeerInfo> = new Map();
  private internetDiscoveryTimer: NodeJS.Timeout | null = null;
  
  // Callbacks
  private onPeerDiscoveredCallbacks: PeerDiscoveredCallback[] = [];
  private onPeerLostCallbacks: PeerLostCallback[] = [];
  private onSyncStatusUpdatedCallbacks: SyncStatusUpdatedCallback[] = [];

  constructor(deviceId?: string) {
    if (!isMobileApp()) {
      console.warn('[MobileP2PSync] Warning: This class is for mobile devices only');
    }
    this.deviceId = deviceId || uuidv4();
    this.discovery = new ZeroConfDiscovery(this.deviceId);
  }

  /**
   * Initialize the P2P discovery system for mobile (client only)
   * Mobile devices only listen for desktop hub broadcasts
   */
  async initialize(pouchDbInstance: any, httpPort?: number): Promise<boolean> {
    if (!isMobileApp()) {
      console.log('Mobile P2P discovery is only for mobile apps');
      return false;
    }
    
    this.pouchDbInstance = pouchDbInstance;
    
    try {
      logZeroconfMessage(`📱 Initializing mobile client for desktop hub discovery`);
      const initialized = await this.discovery.initialize(0); // Port not used for mobile clients
      if (!initialized) {
        throw new Error('Failed to initialize ZeroConf discovery');
      }
      
      // Mobile device only watches for desktop hubs - no publishing/broadcasting
      const startedWatching = await this.discovery.startWatching();
      if (!startedWatching) {
        throw new Error('Failed to start watching for desktop hub services');
      }
      
      // Enhanced peer discovery with multi-master auto-sync
      this.discovery.onPeerDiscovered((peer) => {
        logZeroconfMessage(`📱 Mobile discovered desktop hub: ${peer.hostname}`);
        this.notifyPeerDiscovered(peer);
        
        // Auto-sync with new peer if multi-master is enabled
        if (this.multiMasterConfig.enabled && this.multiMasterConfig.autoSync) {
          this.autoSyncWithNewPeer(peer);
        }
      });
      
      this.discovery.onPeerLost((peerId) => {
        logZeroconfMessage(`📱 Mobile lost connection to desktop hub: ${peerId}`);
        this.notifyPeerLost(peerId);
        
        // Clean up syncs with lost peer
        this.handlePeerLost(peerId);
      });
      
      // Set up internet sync fallback if enabled
      if (this.internetSyncConfig.enabled) {
        this.startInternetDiscovery();
      }
      
      this.isInitialized = true;
      return true;
    } catch (err) {
      console.error('Failed to initialize mobile P2P sync:', err);
      return false;
    }
  }

  /**
   * Configure internet sync for fallback when local peers are unavailable
   */
  configureInternetSync(config: Partial<InternetSyncConfig>): void {
    this.internetSyncConfig = {
      ...this.internetSyncConfig,
      ...config
    };
    
    logZeroconfMessage(`🌐 Internet sync configured: ${JSON.stringify(this.internetSyncConfig)}`);
    
    // Start internet discovery if enabled and initialized
    if (this.internetSyncConfig.enabled && this.isInitialized) {
      this.startInternetDiscovery();
    }
  }

  /**
   * Configure multi-master sync for specific databases
   */
  configureMultiMaster(config: Partial<MultiMasterConfig>): void {
    this.multiMasterConfig = {
      ...this.multiMasterConfig,
      ...config
    };
    
    // Update the set of multi-master databases
    if (config.databases) {
      this.multiMasterDatabases.clear();
      config.databases.forEach(db => this.multiMasterDatabases.add(db));
    }
    
    logZeroconfMessage(`📱 Multi-master configured: ${JSON.stringify(this.multiMasterConfig)}`);
    
    // If enabling multi-master and auto-sync, sync with all current peers
    if (this.multiMasterConfig.enabled && this.multiMasterConfig.autoSync) {
      this.syncWithAllPeers();
    }
  }

  /**
   * Enable multi-master sync for specific databases
   */
  enableMultiMasterSync(databases: string[], direction: 'push' | 'pull' | 'both' = 'both'): void {
    this.configureMultiMaster({
      enabled: true,
      databases,
      autoSync: true,
      direction
    });
  }

  /**
   * Disable multi-master sync
   */
  disableMultiMasterSync(): void {
    this.configureMultiMaster({
      enabled: false,
      databases: [],
      autoSync: false
    });
    
    // Stop all multi-master syncs
    this.stopAllMultiMasterSyncs();
  }

  /**
   * Start multi-master sync for a specific database with ALL discovered peers
   */
  async startMultiMasterSync(dbName: string, direction: 'push' | 'pull' | 'both' = 'both'): Promise<{ success: number; failed: number; results: any[] }> {
    if (!this.isInitialized) {
      throw new Error('Mobile P2P sync not initialized');
    }

    const peers = this.discovery.getPeers().filter(p => p.platform === 'desktop');
    if (peers.length === 0) {
      logZeroconfMessage(`📱 No desktop hubs found for multi-master sync of ${dbName}`);
      return { success: 0, failed: 0, results: [] };
    }

    logZeroconfMessage(`📱 Starting multi-master sync for ${dbName} with ${peers.length} desktop hubs`);
    
    const results = [];
    let successCount = 0;
    let failedCount = 0;

    // Add to multi-master databases set
    this.multiMasterDatabases.add(dbName);

    // Start sync with each peer
    for (const peer of peers) {
      try {
        const result = await this.startSync(peer.id, dbName, direction);
        results.push({ peer: peer.hostname, success: true, result });
        successCount++;
        logZeroconfMessage(`📱 ✅ Multi-master sync started with ${peer.hostname} for ${dbName}`);
      } catch (error) {
        results.push({ peer: peer.hostname, success: false, error: (error as Error).message });
        failedCount++;
        logZeroconfMessage(`📱 ❌ Failed to start multi-master sync with ${peer.hostname} for ${dbName}: ${(error as Error).message}`);
      }
    }

    logZeroconfMessage(`📱 Multi-master sync for ${dbName}: ${successCount} success, ${failedCount} failed`);
    return { success: successCount, failed: failedCount, results };
  }

  /**
   * Stop multi-master sync for a specific database
   */
  async stopMultiMasterSync(dbName: string): Promise<void> {
    logZeroconfMessage(`📱 Stopping multi-master sync for ${dbName}`);
    
    const peers = this.discovery.getPeers();
    for (const peer of peers) {
      try {
        await this.stopSync(peer.id, dbName);
      } catch (error) {
        console.warn(`Failed to stop sync with ${peer.hostname} for ${dbName}:`, error);
      }
    }
    
    // Remove from multi-master databases set
    this.multiMasterDatabases.delete(dbName);
  }

  /**
   * Get multi-master sync status
   */
  getMultiMasterStatus(): { 
    config: MultiMasterConfig; 
    databases: string[]; 
    activeSyncs: number; 
    connectedPeers: number;
    syncsByDatabase: Record<string, number>;
  } {
    const syncsByDatabase: Record<string, number> = {};
    
    // Count active syncs per database
    for (const [syncKey] of this.activeSyncs) {
      const dbName = syncKey.split('-').slice(1).join('-'); // Handle db names with hyphens
      syncsByDatabase[dbName] = (syncsByDatabase[dbName] || 0) + 1;
    }
    
    return {
      config: { ...this.multiMasterConfig },
      databases: Array.from(this.multiMasterDatabases),
      activeSyncs: this.activeSyncs.size,
      connectedPeers: this.discovery.getPeers().length,
      syncsByDatabase
    };
  }

  /**
   * Start internet discovery to find remote peers via server
   */
  private async startInternetDiscovery(): Promise<void> {
    if (!this.internetSyncConfig.enabled || !this.internetSyncConfig.serverUrl || !this.internetSyncConfig.authToken) {
      return;
    }

    logZeroconfMessage(`🌐 Starting internet peer discovery`);
    
    // Clear existing timer
    if (this.internetDiscoveryTimer) {
      clearInterval(this.internetDiscoveryTimer);
    }
    
    // Initial discovery
    await this.discoverInternetPeers();
    
    // Set up periodic discovery
    this.internetDiscoveryTimer = setInterval(() => {
      this.discoverInternetPeers();
    }, 30000); // Every 30 seconds
  }

  /**
   * Discover internet peers via server API
   */
  private async discoverInternetPeers(): Promise<void> {
    try {
      const response = await fetch(`${this.internetSyncConfig.serverUrl}/api/sync/discover-peers`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${this.internetSyncConfig.authToken}`,
          'Content-Type': 'application/json'
        }
      });
      
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      
      const data = await response.json();
      const peers = data.peers || [];
      
      // Process discovered peers
      for (const peerData of peers) {
        if (peerData.deviceType === 'desktop' && peerData.id !== this.deviceId) {
          const internetPeer: InternetPeerInfo = {
            id: peerData.id,
            ip: peerData.ipAddress,
            port: peerData.couchdbPort,
            hostname: peerData.hostname,
            platform: 'desktop',
            isInternetPeer: true,
            proxyUrl: `${this.internetSyncConfig.serverUrl}/api/sync/proxy/${peerData.id}`
          };
          
          // Check if this is a new peer
          if (!this.internetPeers.has(internetPeer.id)) {
            this.internetPeers.set(internetPeer.id, internetPeer);
            logZeroconfMessage(`🌐 ✅ Discovered internet peer: ${internetPeer.hostname}`);
            this.notifyPeerDiscovered(internetPeer);
            
            // Auto-sync if enabled
            if (this.multiMasterConfig.enabled && this.multiMasterConfig.autoSync) {
              this.autoSyncWithNewPeer(internetPeer);
            }
          }
        }
      }
      
      // Clean up peers that are no longer available
      const currentPeerIds = new Set(peers.filter(p => p.deviceType === 'desktop').map(p => p.id));
      for (const [peerId, peer] of this.internetPeers) {
        if (!currentPeerIds.has(peerId)) {
          this.internetPeers.delete(peerId);
          logZeroconfMessage(`🌐 👋 Lost internet peer: ${peer.hostname}`);
          this.notifyPeerLost(peerId);
          this.handlePeerLost(peerId);
        }
      }
      
    } catch (error) {
      console.error('Error discovering internet peers:', error);
      logZeroconfMessage(`🌐 ❌ Internet discovery failed: ${(error as Error).message}`);
    }
  }

  /**
   * Auto-sync with a newly discovered peer (if multi-master is enabled)
   */
  private async autoSyncWithNewPeer(peer: PeerInfo): Promise<void> {
    if (!this.multiMasterConfig.enabled || !this.multiMasterConfig.autoSync) {
      return;
    }

    logZeroconfMessage(`📱 Auto-syncing with new peer ${peer.hostname} for multi-master databases`);
    
    for (const dbName of this.multiMasterDatabases) {
      try {
        await this.startSync(peer.id, dbName, this.multiMasterConfig.direction);
        logZeroconfMessage(`📱 ✅ Auto-sync started with ${peer.hostname} for ${dbName}`);
      } catch (error) {
        logZeroconfMessage(`📱 ❌ Auto-sync failed with ${peer.hostname} for ${dbName}: ${(error as Error).message}`);
      }
    }
  }

  /**
   * Handle peer lost - clean up syncs
   */
  private async handlePeerLost(peerId: string): Promise<void> {
    logZeroconfMessage(`📱 Cleaning up syncs for lost peer: ${peerId}`);
    
    try {
      await this.stopAllSyncsWithPeer(peerId);
    } catch (error) {
      console.warn(`Error cleaning up syncs for lost peer ${peerId}:`, error);
    }
  }

  /**
   * Sync with all currently discovered peers for multi-master databases
   */
  private async syncWithAllPeers(): Promise<void> {
    if (!this.multiMasterConfig.enabled) {
      return;
    }

    const peers = this.discovery.getPeers().filter(p => p.platform === 'desktop');
    logZeroconfMessage(`📱 Syncing with all ${peers.length} peers for multi-master databases`);
    
    for (const dbName of this.multiMasterDatabases) {
      for (const peer of peers) {
        try {
          await this.startSync(peer.id, dbName, this.multiMasterConfig.direction);
        } catch (error) {
          console.warn(`Failed to sync ${dbName} with ${peer.hostname}:`, error);
        }
      }
    }
  }

  /**
   * Stop all multi-master syncs
   */
  private async stopAllMultiMasterSyncs(): Promise<void> {
    logZeroconfMessage(`📱 Stopping all multi-master syncs`);
    
    for (const dbName of this.multiMasterDatabases) {
      await this.stopMultiMasterSync(dbName);
    }
  }

  /**
   * Get discovered desktop hubs (local and internet)
   */
  getPeers(): PeerInfo[] {
    const localPeers = this.discovery.getPeers();
    const internetPeers = Array.from(this.internetPeers.values());
    return [...localPeers, ...internetPeers];
  }

  getSyncStatus(): SyncStatus[] {
    return [...this.syncStatusList];
  }

  onPeerDiscovered(callback: PeerDiscoveredCallback): void {
    this.onPeerDiscoveredCallbacks.push(callback);
  }

  onPeerLost(callback: PeerLostCallback): void {
    this.onPeerLostCallbacks.push(callback);
  }

  onSyncStatusUpdated(callback: SyncStatusUpdatedCallback): void {
    this.onSyncStatusUpdatedCallbacks.push(callback);
  }

  /**
   * Start sync with a desktop hub (local or internet)
   * Only accepts desktop peers - mobile cannot sync with other mobiles
   */
  async startSync(peerId: string, dbName: string, direction: 'push' | 'pull' | 'both' = 'pull'): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Mobile P2P sync not initialized');
    }

    // CRITICAL: Validate restaurant context before any sync operation
    const authToken = this.internetSyncConfig.authToken;
    if (authToken) {
      const restaurantValidation = validateRestaurantContext(authToken);
      if (!restaurantValidation.isValid) {
        throw new Error(`Restaurant validation failed: ${restaurantValidation.error}`);
      }

      // Validate database belongs to this restaurant
      if (!validateDatabaseName(dbName, restaurantValidation.restaurantId!)) {
        throw new Error(`SECURITY: Database ${dbName} does not belong to restaurant ${restaurantValidation.restaurantId}`);
      }

      // Lock restaurant context to prevent mid-session changes
      if (!validateRestaurantLock(restaurantValidation.restaurantId!)) {
        throw new Error(`SECURITY: Restaurant context changed during session - sync blocked`);
      }
      lockRestaurantContext(restaurantValidation.restaurantId!);
    }
    
    // Check both local and internet peers
    let peer = this.discovery.getPeers().find(p => p.id === peerId);
    let isInternetPeer = false;
    
    if (!peer) {
      peer = this.internetPeers.get(peerId);
      isInternetPeer = true;
    }
    
    if (!peer) {
      throw new Error(`Desktop hub ${peerId} not found or not available`);
    }
    
    // Explicitly check that this is a desktop peer
    if (peer.platform !== 'desktop') {
      throw new Error(`Cannot sync with ${peer.platform} peers. Mobile can only sync with desktop hubs.`);
    }
    
    const syncKey = `${peerId}-${dbName}`;
    if (this.activeSyncs.has(syncKey)) {
      console.log(`Sync already active for ${dbName} with desktop hub ${peer.hostname}`);
      return { already: true, syncKey };
    }
    
    try {
      // Determine sync URL based on peer type
      let remoteUrl: string;
      
      if (isInternetPeer && (peer as InternetPeerInfo).proxyUrl) {
        // Internet peer - use proxy URL
        remoteUrl = `${(peer as InternetPeerInfo).proxyUrl}/${dbName}`;
        console.log(`Starting internet sync for ${dbName} with desktop hub ${peer.hostname} via proxy`);
      } else {
        // Local peer - direct connection
        remoteUrl = `http://admin:admin@${peer.ip}:${peer.port}/${dbName}`;
        console.log(`Starting local sync for ${dbName} with desktop hub ${peer.hostname}`);
      }
      
      // Optimized sync options for mobile client
      const syncOptions: any = {
        live: true,
        retry: true,
        heartbeat: 10000,
        ajax: {
          timeout: 30000,
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json'
          }
        },
        batch_size: 50,
        back_off_function: function (delay: number) {
          if (delay === 0) return 1000;
          return Math.min(delay * 1.5, 60000);
        }
      };
      
      // Add authentication based on peer type
      if (isInternetPeer) {
        // Internet peer - use Bearer token
        syncOptions.ajax.headers['Authorization'] = `Bearer ${this.internetSyncConfig.authToken}`;
      } else {
        // Local peer - use basic auth
        syncOptions.auth = {
          username: 'admin',
          password: 'admin'
        };
      }
      
      // Get local database instance
      let localDb;
      
      if (typeof this.pouchDbInstance === 'function') {
        localDb = new this.pouchDbInstance(dbName);
      } else if (this.pouchDbInstance && typeof this.pouchDbInstance.getLocalDb === 'function') {
        localDb = this.pouchDbInstance.getLocalDb(dbName);
      } else if (this.pouchDbInstance && typeof this.pouchDbInstance.getDatabase === 'function') {
        const database = this.pouchDbInstance.getDatabase();
        if (database && typeof database.localDb === 'object') {
          localDb = database.localDb;
        } else {
          throw new Error(`Cannot access local database through V4 proxy`);
        }
      } else {
        throw new Error(`Failed to get a valid PouchDB instance for ${dbName}`);
      }
      
      if (!localDb) {
        throw new Error(`Failed to get or create local database ${dbName}`);
      }
      
      // Perform initial one-time pull to sync data with desktop hub
      try {
        console.log(`Performing initial data sync from desktop hub ${peer.hostname} for ${dbName}`);
        const initialPullOptions = {
          ...syncOptions,
          live: false,
          retry: false
        };
        
        const initialPull = await localDb.replicate.from(remoteUrl, initialPullOptions);
        console.log(`Initial sync complete for ${dbName}:`, 
                   `${initialPull.docs_read} docs read, ${initialPull.docs_written} docs written`);
      } catch (initialPullError) {
        console.warn(`Initial pull had issues for ${dbName}:`, initialPullError);
      }
      
      // Now set up the live replication
      const syncStatus: SyncStatus = {
        peerId,
        dbName,
        status: 'active',
        direction,
        progress: {
          docs_read: 0,
          docs_written: 0,
          pending: 0
        }
      };
      
      this.syncStatusList.push(syncStatus);
      
      let sync: any;
      
      // Mobile as client: prefer PULL from desktop hub (default)
      if (direction === 'push') {
        sync = localDb.replicate.to(remoteUrl, syncOptions);
      } else if (direction === 'pull') {
        sync = localDb.replicate.from(remoteUrl, syncOptions);
      } else if (direction === 'both') {
        // Bidirectional sync
        console.log(`Starting bidirectional sync for ${dbName} with desktop hub ${peer.hostname}`);
        sync = localDb.sync(remoteUrl, syncOptions);
      }
      
      sync.on('change', (info: any) => {
        console.log(`Sync change for ${dbName} with desktop hub ${peer.hostname}:`, info);
        
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].progress = {
            docs_read: info.docs_read || 0,
            docs_written: info.docs_written || 0,
            pending: info.pending || 0
          };
          
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
      });
      
      sync.on('paused', (err: any) => {
        console.log(`Sync paused for ${dbName} with desktop hub ${peer.hostname}:`, err);
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].status = 'paused';
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
      });
      
      sync.on('active', () => {
        console.log(`Sync active for ${dbName} with desktop hub ${peer.hostname}`);
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].status = 'active';
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
      });
      
      sync.on('denied', (err: any) => {
        console.error(`Sync denied for ${dbName} with desktop hub ${peer.hostname}:`, err);
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].status = 'error';
          this.syncStatusList[statusIndex].error = err.message || 'Sync denied';
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
      });
      
      sync.on('complete', (info: any) => {
        console.log(`Sync complete for ${dbName} with desktop hub ${peer.hostname}:`, info);
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].status = 'complete';
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
        this.activeSyncs.delete(syncKey);
      });
      
      sync.on('error', (err: any) => {
        console.error(`Sync error for ${dbName} with desktop hub ${peer.hostname}:`, err);
        const statusIndex = this.syncStatusList.findIndex(s => s.peerId === peerId && s.dbName === dbName);
        if (statusIndex >= 0) {
          this.syncStatusList[statusIndex].status = 'error';
          this.syncStatusList[statusIndex].error = err.message || 'Unknown sync error';
          this.onSyncStatusUpdatedCallbacks.forEach(callback => callback(this.syncStatusList[statusIndex]));
        }
        this.activeSyncs.delete(syncKey);
      });
      
      this.activeSyncs.set(syncKey, sync);
      
      return {
        syncKey,
        peer: peer.hostname,
        direction,
        database: dbName
      };
      
    } catch (error) {
      console.error(`Failed to start sync for ${dbName} with desktop hub ${peerId}:`, error);
      
      // Remove failed sync status
      this.syncStatusList = this.syncStatusList.filter(s => !(s.peerId === peerId && s.dbName === dbName));
      
      throw error;
    }
  }

  /**
   * Stop sync with a desktop hub
   */
  async stopSync(peerId: string, dbName: string): Promise<void> {
    const syncKey = `${peerId}-${dbName}`;
    const sync = this.activeSyncs.get(syncKey);
    
    if (sync && typeof sync.cancel === 'function') {
      sync.cancel();
      this.activeSyncs.delete(syncKey);
      console.log(`Stopped sync for ${dbName} with desktop hub ${peerId}`);
    }
  }

  /**
   * Stop all syncs with a specific desktop hub
   */
  async stopAllSyncsWithPeer(peerId: string): Promise<void> {
    for (const [syncKey, sync] of this.activeSyncs.entries()) {
      if (syncKey.startsWith(`${peerId}-`)) {
        if (sync && typeof sync.cancel === 'function') {
          sync.cancel();
        }
        this.activeSyncs.delete(syncKey);
      }
    }
  }

  /**
   * Clean up all syncs and close discovery
   */
  async cleanup(): Promise<void> {
    // Cancel all active syncs
    for (const sync of this.activeSyncs.values()) {
      if (sync && typeof sync.cancel === 'function') {
        sync.cancel();
      }
    }
    this.activeSyncs.clear();
    
    // Stop internet discovery
    if (this.internetDiscoveryTimer) {
      clearInterval(this.internetDiscoveryTimer);
      this.internetDiscoveryTimer = null;
    }
    
    // Clear internet peers
    this.internetPeers.clear();
    
    // Clean up the discovery service
    await this.discovery.close();
    this.isInitialized = false;
  }

  public getMdnsStatus(): 'not_running' | 'running' | 'error' {
    return this.discovery.getMdnsStatus();
  }

  private notifyPeerDiscovered(peer: PeerInfo): void {
    this.onPeerDiscoveredCallbacks.forEach(callback => callback(peer));
  }

  private notifyPeerLost(peerId: string): void {
    this.onPeerLostCallbacks.forEach(callback => callback(peerId));
  }
} 