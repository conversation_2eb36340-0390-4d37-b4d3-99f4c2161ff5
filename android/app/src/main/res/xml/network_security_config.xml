<?xml version="1.0" encoding="utf-8"?>
<network-security-config>
    <domain-config cleartextTrafficPermitted="true">
        <!-- Allow HTTP traffic to localhost for development -->
        <domain includeSubdomains="false">localhost</domain>
        <domain includeSubdomains="false">127.0.0.1</domain>
        <domain includeSubdomains="false">********</domain>
        <!-- Allow HTTP traffic to local network IPs for development -->
        <domain includeSubdomains="false">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">***********</domain>
        <domain includeSubdomains="true">10.0.0.0</domain>
        <!-- Allow HTTP traffic to bistro.icu for production -->
        <domain includeSubdomains="true">bistro.icu</domain>
    </domain-config>
    
    <!-- Base configuration for all other domains -->
    <base-config cleartextTrafficPermitted="false">
        <trust-anchors>
            <certificates src="system"/>
        </trust-anchors>
    </base-config>
</network-security-config>