import { PeerInfo } from './zeroconf-discovery';
import { MobileP2PSync } from './mobile-p2p-sync';
import { ConnectionManager } from './connection-manager';
import { logZeroconfMessage } from './zeroconf-discovery';

export interface FallbackConfig {
  enabled: boolean;
  localDiscoveryTimeout: number;
  internetFallbackDelay: number;
  preferLocalOverInternet: boolean;
  internetSyncConfig: {
    serverUrl: string;
    authToken: string | null;
    retryAttempts: number;
    retryDelay: number;
  };
  syncStrategy: 'local_only' | 'internet_only' | 'local_preferred' | 'internet_preferred' | 'parallel';
}

export interface FallbackState {
  phase: 'local_discovery' | 'internet_fallback' | 'parallel_sync' | 'local_active' | 'internet_active' | 'error';
  localPeers: PeerInfo[];
  internetPeers: PeerInfo[];
  activeSyncType: 'local' | 'internet' | 'both' | 'none';
  lastFallbackTime?: Date;
  error?: string;
}

export type FallbackStateCallback = (state: FallbackState) => void;

export class FallbackCoordinator {
  private config: FallbackConfig;
  private state: FallbackState;
  private mobileP2PSync: MobileP2PSync;
  private connectionManager: ConnectionManager;
  private isActive: boolean = false;
  private fallbackTimer: NodeJS.Timeout | null = null;
  private stateCallbacks: FallbackStateCallback[] = [];

  constructor(
    mobileP2PSync: MobileP2PSync,
    connectionManager: ConnectionManager,
    config: Partial<FallbackConfig> = {}
  ) {
    this.mobileP2PSync = mobileP2PSync;
    this.connectionManager = connectionManager;
    this.config = {
      enabled: true,
      localDiscoveryTimeout: 60000, // 60 seconds
      internetFallbackDelay: 5000, // 5 seconds after local timeout
      preferLocalOverInternet: true,
      internetSyncConfig: {
        serverUrl: process.env.NEXT_PUBLIC_SYNC_SERVER_URL || '',
        authToken: null,
        retryAttempts: 3,
        retryDelay: 10000
      },
      syncStrategy: 'local_preferred',
      ...config
    };

    this.state = {
      phase: 'local_discovery',
      localPeers: [],
      internetPeers: [],
      activeSyncType: 'none'
    };
  }

  /**
   * Start the fallback coordinator
   */
  start(): void {
    if (!this.config.enabled || this.isActive) {
      return;
    }

    this.isActive = true;
    logZeroconfMessage('🔄 Fallback coordinator started');

    // Start with local discovery phase
    this.startLocalDiscovery();
  }

  /**
   * Stop the fallback coordinator
   */
  stop(): void {
    if (!this.isActive) {
      return;
    }

    this.isActive = false;
    logZeroconfMessage('🔄 Fallback coordinator stopped');

    // Clear fallback timer
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }

    this.updateState({
      phase: 'local_discovery',
      activeSyncType: 'none'
    });
  }

  /**
   * Configure fallback settings
   */
  configure(config: Partial<FallbackConfig>): void {
    this.config = { ...this.config, ...config };
    logZeroconfMessage(`🔄 Fallback configured: ${JSON.stringify(this.config)}`);
  }

  /**
   * Get current state
   */
  getState(): FallbackState {
    return { ...this.state };
  }

  /**
   * Subscribe to state changes
   */
  onStateChanged(callback: FallbackStateCallback): void {
    this.stateCallbacks.push(callback);
  }

  /**
   * Handle peer discovered event
   */
  onPeerDiscovered(peer: PeerInfo): void {
    if (!this.isActive) {
      return;
    }

    const isInternetPeer = (peer as any).isInternetPeer === true;
    
    if (isInternetPeer) {
      this.state.internetPeers.push(peer);
      logZeroconfMessage(`🔄 🌐 Internet peer discovered: ${peer.hostname}`);
    } else {
      this.state.localPeers.push(peer);
      logZeroconfMessage(`🔄 🏠 Local peer discovered: ${peer.hostname}`);
      
      // If we're in local discovery phase and found local peers, activate local sync
      if (this.state.phase === 'local_discovery') {
        this.activateLocalSync();
      }
    }

    this.evaluateSyncStrategy();
  }

  /**
   * Handle peer lost event
   */
  onPeerLost(peerId: string): void {
    if (!this.isActive) {
      return;
    }

    // Remove from local peers
    this.state.localPeers = this.state.localPeers.filter(p => p.id !== peerId);
    
    // Remove from internet peers
    this.state.internetPeers = this.state.internetPeers.filter(p => p.id !== peerId);

    logZeroconfMessage(`🔄 Peer lost: ${peerId}`);
    this.evaluateSyncStrategy();
  }

  /**
   * Start local discovery phase
   */
  private startLocalDiscovery(): void {
    logZeroconfMessage('🔄 🏠 Starting local discovery phase');
    
    this.updateState({
      phase: 'local_discovery',
      activeSyncType: 'none'
    });

    // Set timeout for local discovery
    this.fallbackTimer = setTimeout(() => {
      this.handleLocalDiscoveryTimeout();
    }, this.config.localDiscoveryTimeout);
  }

  /**
   * Handle local discovery timeout
   */
  private handleLocalDiscoveryTimeout(): void {
    if (!this.isActive) {
      return;
    }

    logZeroconfMessage('🔄 ⏰ Local discovery timeout');

    if (this.state.localPeers.length > 0) {
      // Local peers found, activate local sync
      this.activateLocalSync();
    } else {
      // No local peers, start internet fallback
      this.startInternetFallback();
    }
  }

  /**
   * Start internet fallback
   */
  private startInternetFallback(): void {
    logZeroconfMessage('🔄 🌐 Starting internet fallback');
    
    this.updateState({
      phase: 'internet_fallback',
      lastFallbackTime: new Date()
    });

    // Configure internet sync
    this.configureInternetSync();

    // Set delay before activating internet sync
    this.fallbackTimer = setTimeout(() => {
      this.activateInternetSync();
    }, this.config.internetFallbackDelay);
  }

  /**
   * Configure internet sync
   */
  private configureInternetSync(): void {
    if (!this.config.internetSyncConfig.serverUrl) {
      logZeroconfMessage('🔄 🌐 ❌ Internet sync server URL not configured');
      this.updateState({
        phase: 'error',
        error: 'Internet sync server URL not configured'
      });
      return;
    }

    // Get auth token (in real app, this would come from user auth)
    const authToken = this.getAuthToken();
    if (!authToken) {
      logZeroconfMessage('🔄 🌐 ❌ Internet sync auth token not available');
      this.updateState({
        phase: 'error',
        error: 'Internet sync auth token not available'
      });
      return;
    }

    // Configure internet sync in MobileP2PSync
    this.mobileP2PSync.configureInternetSync({
      enabled: true,
      serverUrl: this.config.internetSyncConfig.serverUrl,
      authToken: authToken,
      fallbackDelay: this.config.internetFallbackDelay
    });

    logZeroconfMessage('🔄 🌐 ✅ Internet sync configured');
  }

  /**
   * Activate local sync
   */
  private activateLocalSync(): void {
    logZeroconfMessage(`🔄 🏠 ✅ Activating local sync with ${this.state.localPeers.length} peers`);
    
    // Cancel fallback timer if running
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }

    this.updateState({
      phase: 'local_active',
      activeSyncType: 'local'
    });

    // Add local peers to connection manager
    this.state.localPeers.forEach(peer => {
      this.connectionManager.addPeer(peer);
    });
  }

  /**
   * Activate internet sync
   */
  private activateInternetSync(): void {
    logZeroconfMessage(`🔄 🌐 ✅ Activating internet sync`);
    
    this.updateState({
      phase: 'internet_active',
      activeSyncType: 'internet'
    });

    // Wait for internet peers to be discovered
    setTimeout(() => {
      const internetPeers = this.mobileP2PSync.getPeers().filter(p => (p as any).isInternetPeer);
      
      if (internetPeers.length > 0) {
        this.state.internetPeers = internetPeers;
        internetPeers.forEach(peer => {
          this.connectionManager.addPeer(peer);
        });
        logZeroconfMessage(`🔄 🌐 ✅ Connected to ${internetPeers.length} internet peers`);
      } else {
        logZeroconfMessage('🔄 🌐 ❌ No internet peers found');
        this.updateState({
          phase: 'error',
          error: 'No internet peers found'
        });
      }
    }, 5000); // Wait 5 seconds for internet discovery
  }

  /**
   * Evaluate sync strategy based on current state
   */
  private evaluateSyncStrategy(): void {
    if (!this.isActive) {
      return;
    }

    const hasLocalPeers = this.state.localPeers.length > 0;
    const hasInternetPeers = this.state.internetPeers.length > 0;

    logZeroconfMessage(`🔄 📊 Evaluating sync strategy: local=${hasLocalPeers}, internet=${hasInternetPeers}, strategy=${this.config.syncStrategy}`);

    switch (this.config.syncStrategy) {
      case 'local_only':
        if (hasLocalPeers && this.state.activeSyncType !== 'local') {
          this.activateLocalSync();
        }
        break;

      case 'internet_only':
        if (hasInternetPeers && this.state.activeSyncType !== 'internet') {
          this.activateInternetSync();
        }
        break;

      case 'local_preferred':
        if (hasLocalPeers && this.state.activeSyncType !== 'local') {
          this.activateLocalSync();
        } else if (!hasLocalPeers && hasInternetPeers && this.state.activeSyncType !== 'internet') {
          this.activateInternetSync();
        }
        break;

      case 'internet_preferred':
        if (hasInternetPeers && this.state.activeSyncType !== 'internet') {
          this.activateInternetSync();
        } else if (!hasInternetPeers && hasLocalPeers && this.state.activeSyncType !== 'local') {
          this.activateLocalSync();
        }
        break;

      case 'parallel':
        if ((hasLocalPeers || hasInternetPeers) && this.state.activeSyncType !== 'both') {
          this.activateParallelSync();
        }
        break;
    }
  }

  /**
   * Activate parallel sync (both local and internet)
   */
  private activateParallelSync(): void {
    logZeroconfMessage('🔄 🔀 Activating parallel sync');
    
    this.updateState({
      phase: 'parallel_sync',
      activeSyncType: 'both'
    });

    // Add all peers to connection manager
    [...this.state.localPeers, ...this.state.internetPeers].forEach(peer => {
      this.connectionManager.addPeer(peer);
    });
  }

  /**
   * Get auth token for internet sync
   */
  private getAuthToken(): string | null {
    // In real implementation, this would get token from:
    // - Secure storage
    // - User authentication context
    // - Environment variables
    // - Configuration service
    
    return this.config.internetSyncConfig.authToken || 
           process.env.NEXT_PUBLIC_SYNC_AUTH_TOKEN || 
           null;
  }

  /**
   * Update state and notify callbacks
   */
  private updateState(updates: Partial<FallbackState>): void {
    this.state = { ...this.state, ...updates };
    this.stateCallbacks.forEach(callback => {
      try {
        callback(this.state);
      } catch (error) {
        console.error('Error in fallback state callback:', error);
      }
    });
  }

  /**
   * Get human-readable status message
   */
  getStatusMessage(): string {
    switch (this.state.phase) {
      case 'local_discovery':
        return 'Discovering local devices...';
      case 'internet_fallback':
        return 'Trying internet sync...';
      case 'parallel_sync':
        return 'Syncing with both local and internet peers';
      case 'local_active':
        return `Syncing with ${this.state.localPeers.length} local device(s)`;
      case 'internet_active':
        return `Syncing with ${this.state.internetPeers.length} internet peer(s)`;
      case 'error':
        return `Error: ${this.state.error}`;
      default:
        return 'Unknown status';
    }
  }

  /**
   * Get sync summary
   */
  getSyncSummary(): {
    phase: string;
    activeSyncType: string;
    localPeers: number;
    internetPeers: number;
    totalPeers: number;
    lastFallbackTime?: Date;
  } {
    return {
      phase: this.state.phase,
      activeSyncType: this.state.activeSyncType,
      localPeers: this.state.localPeers.length,
      internetPeers: this.state.internetPeers.length,
      totalPeers: this.state.localPeers.length + this.state.internetPeers.length,
      lastFallbackTime: this.state.lastFallbackTime
    };
  }

  /**
   * Force switch to internet sync
   */
  forceSwitchToInternet(): void {
    if (!this.isActive) {
      return;
    }

    logZeroconfMessage('🔄 🌐 Forcing switch to internet sync');
    this.configureInternetSync();
    this.activateInternetSync();
  }

  /**
   * Force switch to local sync
   */
  forceSwitchToLocal(): void {
    if (!this.isActive || this.state.localPeers.length === 0) {
      return;
    }

    logZeroconfMessage('🔄 🏠 Forcing switch to local sync');
    this.activateLocalSync();
  }
}