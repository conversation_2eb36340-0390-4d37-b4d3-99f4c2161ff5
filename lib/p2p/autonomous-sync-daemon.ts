import { isMobileApp } from '../utils/environment';
import { MobileP2PSync } from './mobile-p2p-sync';
import { PeerInfo } from './zeroconf-discovery';
import { SyncStatus } from '../../types/p2p-sync';
import { logZeroconfMessage } from './zeroconf-discovery';
import { AutonomousSyncErrorHandler, RetryableError } from './error-handler';

export interface AutonomousSyncConfig {
  enabled: boolean;
  discoveryTimeout: number;
  fallbackDelay: number;
  autoSyncDatabases: string[];
  preferLocalOverInternet: boolean;
  retryAttempts: number;
  retryDelay: number;
}

export interface AutonomousSyncState {
  phase: 'initializing' | 'discovering' | 'connecting' | 'syncing' | 'fallback' | 'error' | 'complete';
  discoveredPeers: PeerInfo[];
  connectedPeers: PeerInfo[];
  syncStatus: SyncStatus[];
  error?: string;
  lastActivity: Date;
}

export type AutonomousSyncStateCallback = (state: AutonomousSyncState) => void;

export class AutonomousSyncDaemon {
  private config: AutonomousSyncConfig;
  private state: AutonomousSyncState;
  private mobileP2PSync: MobileP2PSync;
  private isRunning: boolean = false;
  private discoveryTimer: NodeJS.Timeout | null = null;
  private fallbackTimer: NodeJS.Timeout | null = null;
  private retryTimer: NodeJS.Timeout | null = null;
  private stateCallbacks: AutonomousSyncStateCallback[] = [];
  private errorHandler: AutonomousSyncErrorHandler;

  constructor(mobileP2PSync: MobileP2PSync, config: Partial<AutonomousSyncConfig> = {}) {
    this.mobileP2PSync = mobileP2PSync;
    this.config = {
      enabled: true,
      discoveryTimeout: 60000, // 60 seconds
      fallbackDelay: 5000, // 5 seconds after discovery timeout
      autoSyncDatabases: ['orders', 'staff', 'inventory', 'settings'],
      preferLocalOverInternet: true,
      retryAttempts: 3,
      retryDelay: 10000, // 10 seconds between retries
      ...config
    };

    this.state = {
      phase: 'initializing',
      discoveredPeers: [],
      connectedPeers: [],
      syncStatus: [],
      lastActivity: new Date()
    };

    // Initialize error handler
    this.errorHandler = new AutonomousSyncErrorHandler({
      maxRetries: this.config.retryAttempts,
      initialDelay: this.config.retryDelay,
      maxDelay: 60000, // 1 minute max delay
      backoffMultiplier: 2,
      jitterEnabled: true,
      circuitBreakerThreshold: 5,
      circuitBreakerTimeout: 300000 // 5 minutes
    });
  }

  /**
   * Start the autonomous sync daemon
   */
  async start(): Promise<void> {
    if (!isMobileApp() || this.isRunning) {
      return;
    }

    if (!this.config.enabled) {
      logZeroconfMessage('🤖 Autonomous sync daemon is disabled');
      return;
    }

    logZeroconfMessage('🤖 Starting autonomous sync daemon');
    this.isRunning = true;
    
    this.updateState({
      phase: 'initializing',
      discoveredPeers: [],
      connectedPeers: [],
      syncStatus: [],
      lastActivity: new Date()
    });

    try {
      // Set up event listeners
      this.setupEventListeners();
      
      // Start discovery phase
      await this.startDiscoveryPhase();
      
    } catch (error) {
      logZeroconfMessage(`🤖 ❌ Failed to start autonomous sync: ${(error as Error).message}`);
      this.updateState({
        phase: 'error',
        error: (error as Error).message,
        lastActivity: new Date()
      });
    }
  }

  /**
   * Stop the autonomous sync daemon
   */
  async stop(): Promise<void> {
    if (!this.isRunning) {
      return;
    }

    logZeroconfMessage('🤖 Stopping autonomous sync daemon');
    this.isRunning = false;

    // Clear all timers
    if (this.discoveryTimer) {
      clearTimeout(this.discoveryTimer);
      this.discoveryTimer = null;
    }
    if (this.fallbackTimer) {
      clearTimeout(this.fallbackTimer);
      this.fallbackTimer = null;
    }
    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    // Clean up event listeners
    this.cleanupEventListeners();

    this.updateState({
      phase: 'complete',
      lastActivity: new Date()
    });
  }

  /**
   * Configure the autonomous sync daemon
   */
  configure(config: Partial<AutonomousSyncConfig>): void {
    this.config = { ...this.config, ...config };
    logZeroconfMessage(`🤖 Autonomous sync configured: ${JSON.stringify(this.config)}`);
  }

  /**
   * Get current state
   */
  getState(): AutonomousSyncState {
    return { ...this.state };
  }

  /**
   * Subscribe to state changes
   */
  onStateChanged(callback: AutonomousSyncStateCallback): void {
    this.stateCallbacks.push(callback);
  }

  /**
   * Start the discovery phase
   */
  private async startDiscoveryPhase(): Promise<void> {
    const result = await this.errorHandler.executeWithRetry(
      async () => {
        logZeroconfMessage('🤖 🔍 Starting discovery phase');
        
        this.updateState({
          phase: 'discovering',
          discoveredPeers: [],
          lastActivity: new Date()
        });

        // Set discovery timeout
        this.discoveryTimer = setTimeout(() => {
          this.handleDiscoveryTimeout();
        }, this.config.discoveryTimeout);

        // Check if we already have peers discovered
        const existingPeers = this.mobileP2PSync.getPeers().filter(p => p.platform === 'desktop');
        if (existingPeers.length > 0) {
          logZeroconfMessage(`🤖 ✅ Found ${existingPeers.length} existing peers`);
          this.handlePeersDiscovered(existingPeers);
        }

        return true;
      },
      'discovery_phase',
      {
        maxRetries: 2,
        timeout: 30000,
        customErrorHandler: (error) => ({
          code: 'DISCOVERY_FAILED',
          message: error.message || 'Discovery phase failed',
          retryable: true,
          category: 'network' as const,
          severity: 'medium' as const,
          timestamp: new Date()
        })
      }
    );

    if (!result.success) {
      logZeroconfMessage(`🤖 ❌ Discovery phase failed: ${result.error?.message}`);
      this.updateState({
        phase: 'error',
        error: result.error?.message,
        lastActivity: new Date()
      });
    }
  }

  /**
   * Handle discovery timeout - start fallback process
   */
  private async handleDiscoveryTimeout(): Promise<void> {
    if (!this.isRunning) return;

    const localPeers = this.state.discoveredPeers.filter(p => !(p as any).isInternetPeer);
    
    if (localPeers.length === 0) {
      logZeroconfMessage('🤖 ⏰ Discovery timeout - no local peers found, starting fallback');
      
      this.updateState({
        phase: 'fallback',
        lastActivity: new Date()
      });

      // Start fallback after delay
      this.fallbackTimer = setTimeout(() => {
        this.startInternetFallback();
      }, this.config.fallbackDelay);
    } else {
      logZeroconfMessage(`🤖 ✅ Discovery complete - found ${localPeers.length} local peers`);
      this.startConnectionPhase();
    }
  }

  /**
   * Start internet fallback when no local peers found
   */
  private async startInternetFallback(): Promise<void> {
    if (!this.isRunning) return;

    logZeroconfMessage('🤖 🌐 Starting internet fallback');

    // Configure internet sync if not already configured
    // This would typically be configured at app startup with user credentials
    const internetConfig = {
      enabled: true,
      serverUrl: process.env.NEXT_PUBLIC_SYNC_SERVER_URL || '',
      authToken: this.getAuthToken(), // Get from storage/context
      fallbackDelay: this.config.fallbackDelay
    };

    if (internetConfig.serverUrl && internetConfig.authToken) {
      this.mobileP2PSync.configureInternetSync(internetConfig);
      
      // Wait a bit for internet discovery to work
      setTimeout(() => {
        const internetPeers = this.mobileP2PSync.getPeers().filter(p => (p as any).isInternetPeer);
        if (internetPeers.length > 0) {
          logZeroconfMessage(`🤖 🌐 ✅ Found ${internetPeers.length} internet peers`);
          this.handlePeersDiscovered(internetPeers);
        } else {
          logZeroconfMessage('🤖 🌐 ❌ No internet peers found either');
          this.handleNoPerersFound();
        }
      }, 10000); // Wait 10 seconds for internet discovery
    } else {
      logZeroconfMessage('🤖 🌐 ❌ Internet sync not configured');
      this.handleNoPerersFound();
    }
  }

  /**
   * Handle when no peers are found (local or internet)
   */
  private handleNoPerersFound(): void {
    logZeroconfMessage('🤖 ❌ No peers found - will retry later');
    
    this.updateState({
      phase: 'error',
      error: 'No desktop hubs found for automatic sync',
      lastActivity: new Date()
    });

    // Schedule retry if configured
    if (this.config.retryAttempts > 0) {
      this.scheduleRetry();
    }
  }

  /**
   * Schedule a retry attempt
   */
  private scheduleRetry(): void {
    if (!this.isRunning) return;

    logZeroconfMessage(`🤖 🔄 Scheduling retry in ${this.config.retryDelay}ms`);
    
    this.retryTimer = setTimeout(() => {
      this.startDiscoveryPhase();
    }, this.config.retryDelay);
  }

  /**
   * Handle peers discovered
   */
  private handlePeersDiscovered(peers: PeerInfo[]): void {
    if (!this.isRunning) return;

    logZeroconfMessage(`🤖 📡 ${peers.length} peers discovered`);
    
    this.updateState({
      discoveredPeers: peers,
      lastActivity: new Date()
    });

    // Cancel discovery timer if still running
    if (this.discoveryTimer) {
      clearTimeout(this.discoveryTimer);
      this.discoveryTimer = null;
    }

    // Start connection phase
    this.startConnectionPhase();
  }

  /**
   * Start the connection phase
   */
  private async startConnectionPhase(): Promise<void> {
    if (!this.isRunning) return;

    logZeroconfMessage('🤖 🔌 Starting connection phase');
    
    this.updateState({
      phase: 'connecting',
      lastActivity: new Date()
    });

    // Prioritize local peers over internet peers
    const localPeers = this.state.discoveredPeers.filter(p => !(p as any).isInternetPeer);
    const internetPeers = this.state.discoveredPeers.filter(p => (p as any).isInternetPeer);
    
    const orderedPeers = this.config.preferLocalOverInternet 
      ? [...localPeers, ...internetPeers]
      : [...internetPeers, ...localPeers];

    // Start sync with all discovered peers
    await this.startSyncWithPeers(orderedPeers);
  }

  /**
   * Start sync with discovered peers
   */
  private async startSyncWithPeers(peers: PeerInfo[]): Promise<void> {
    if (!this.isRunning) return;

    logZeroconfMessage(`🤖 🔄 Starting sync with ${peers.length} peers`);
    
    this.updateState({
      phase: 'syncing',
      connectedPeers: peers,
      lastActivity: new Date()
    });

    // Enable multi-master sync for configured databases
    this.mobileP2PSync.enableMultiMasterSync(this.config.autoSyncDatabases, 'both');

    // Start sync for each database with all peers
    const syncResults = await this.enableAutomaticDatabaseSync(peers);
    
    // Log overall results
    const totalSuccess = syncResults.reduce((sum, result) => sum + result.success, 0);
    const totalFailed = syncResults.reduce((sum, result) => sum + result.failed, 0);
    
    logZeroconfMessage(`🤖 📊 Sync summary: ${totalSuccess} successful, ${totalFailed} failed`);

    // Update state to complete
    this.updateState({
      phase: 'complete',
      lastActivity: new Date()
    });
  }

  /**
   * Enable automatic database sync for all configured databases
   */
  private async enableAutomaticDatabaseSync(peers: PeerInfo[]): Promise<Array<{dbName: string, success: number, failed: number}>> {
    const results: Array<{dbName: string, success: number, failed: number}> = [];
    
    // Detect available databases automatically
    const availableDatabases = await this.detectAvailableDatabases();
    
    // Merge configured databases with detected ones
    const databasesToSync = this.mergeAndPrioritizeDatabases(availableDatabases);
    
    logZeroconfMessage(`🤖 📚 Auto-enabling sync for ${databasesToSync.length} databases`);
    
    // Start sync for each database with error handling
    for (const dbName of databasesToSync) {
      const syncResult = await this.errorHandler.executeWithRetry(
        async () => {
          return await this.mobileP2PSync.startMultiMasterSync(dbName, 'both');
        },
        `sync_${dbName}`,
        {
          maxRetries: 2,
          timeout: 30000,
          customErrorHandler: (error) => ({
            code: 'SYNC_START_FAILED',
            message: `Failed to start sync for ${dbName}: ${error.message}`,
            retryable: true,
            category: 'network' as const,
            severity: 'medium' as const,
            context: { dbName, peersCount: peers.length },
            timestamp: new Date()
          })
        }
      );

      if (syncResult.success && syncResult.result) {
        results.push({
          dbName,
          success: syncResult.result.success,
          failed: syncResult.result.failed
        });
        
        logZeroconfMessage(`🤖 ✅ ${dbName}: ${syncResult.result.success} connected, ${syncResult.result.failed} failed`);
      } else {
        results.push({
          dbName,
          success: 0,
          failed: peers.length
        });
        
        logZeroconfMessage(`🤖 ❌ Failed to start sync for ${dbName}: ${syncResult.error?.message}`);
      }
    }
    
    return results;
  }

  /**
   * Detect available databases from the local PouchDB instance
   */
  private async detectAvailableDatabases(): Promise<string[]> {
    const availableDatabases: string[] = [];
    
    try {
      // Try to detect databases from PouchDB instance
      if (this.mobileP2PSync && typeof this.mobileP2PSync.getPeers === 'function') {
        const peers = this.mobileP2PSync.getPeers();
        
        // If we have peers, try to detect their databases
        if (peers.length > 0) {
          const detectedDbs = await this.detectDatabasesFromPeers(peers);
          availableDatabases.push(...detectedDbs);
        }
      }
      
      // Also check for common restaurant databases
      const commonDatabases = [
        'orders', 'staff', 'inventory', 'menu', 'settings', 
        'customers', 'payments', 'reports', 'shifts', 'tables'
      ];
      
      for (const dbName of commonDatabases) {
        if (!availableDatabases.includes(dbName)) {
          const exists = await this.checkDatabaseExists(dbName);
          if (exists) {
            availableDatabases.push(dbName);
          }
        }
      }
      
      logZeroconfMessage(`🤖 📡 Detected ${availableDatabases.length} available databases`);
      
    } catch (error) {
      logZeroconfMessage(`🤖 ⚠️ Database detection failed: ${(error as Error).message}`);
    }
    
    return availableDatabases;
  }

  /**
   * Detect databases from connected peers
   */
  private async detectDatabasesFromPeers(peers: PeerInfo[]): Promise<string[]> {
    const detectedDatabases: string[] = [];
    
    for (const peer of peers) {
      try {
        // Try to get database list from peer
        const databaseList = await this.fetchDatabaseListFromPeer(peer);
        
        for (const dbName of databaseList) {
          if (!detectedDatabases.includes(dbName)) {
            detectedDatabases.push(dbName);
          }
        }
        
      } catch (error) {
        logZeroconfMessage(`🤖 ⚠️ Failed to detect databases from ${peer.hostname}: ${(error as Error).message}`);
      }
    }
    
    return detectedDatabases;
  }

  /**
   * Fetch database list from a specific peer
   */
  private async fetchDatabaseListFromPeer(peer: PeerInfo): Promise<string[]> {
    const result = await this.errorHandler.executeWithRetry(
      async () => {
        const isInternetPeer = (peer as any).isInternetPeer === true;
        let baseUrl: string;
        
        if (isInternetPeer) {
          // Use proxy URL for internet peers
          baseUrl = (peer as any).proxyUrl || `http://${peer.ip}:${peer.port}`;
        } else {
          // Direct connection for local peers
          baseUrl = `http://admin:admin@${peer.ip}:${peer.port}`;
        }
        
        const response = await fetch(`${baseUrl}/_all_dbs`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json'
          }
        });
        
        if (!response.ok) {
          throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const databases = await response.json();
        
        // Filter out system databases
        const userDatabases = databases.filter((db: string) => 
          !db.startsWith('_') && 
          !db.startsWith('pouch__') &&
          db !== 'couchdb-user-db'
        );
        
        return userDatabases;
      },
      `fetch_databases_${peer.id}`,
      {
        maxRetries: 2,
        timeout: 10000,
        customErrorHandler: (error) => ({
          code: 'DATABASE_LIST_FETCH_FAILED',
          message: `Failed to fetch database list from ${peer.hostname}: ${error.message}`,
          retryable: true,
          category: 'network' as const,
          severity: 'medium' as const,
          context: { peerId: peer.id, peerHostname: peer.hostname },
          timestamp: new Date()
        })
      }
    );

    if (result.success && result.result) {
      return result.result;
    } else {
      logZeroconfMessage(`🤖 ⚠️ Failed to fetch database list from ${peer.hostname}: ${result.error?.message}`);
      return [];
    }
  }

  /**
   * Check if a database exists locally
   */
  private async checkDatabaseExists(dbName: string): Promise<boolean> {
    try {
      // Try to create a PouchDB instance for the database
      if (typeof this.mobileP2PSync === 'object' && this.mobileP2PSync !== null) {
        // This is a simplified check - in a real implementation,
        // you would check the actual PouchDB instance
        return true;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Merge configured databases with detected ones and prioritize
   */
  private mergeAndPrioritizeDatabases(detectedDatabases: string[]): string[] {
    const mergedDatabases: string[] = [];
    
    // First, add configured databases (high priority)
    for (const dbName of this.config.autoSyncDatabases) {
      if (!mergedDatabases.includes(dbName)) {
        mergedDatabases.push(dbName);
      }
    }
    
    // Then, add detected databases (medium priority)
    for (const dbName of detectedDatabases) {
      if (!mergedDatabases.includes(dbName)) {
        mergedDatabases.push(dbName);
      }
    }
    
    // Apply database priority rules
    const prioritizedDatabases = this.applyDatabasePriority(mergedDatabases);
    
    logZeroconfMessage(`🤖 📝 Final database sync list: ${prioritizedDatabases.join(', ')}`);
    
    return prioritizedDatabases;
  }

  /**
   * Apply priority rules to database list
   */
  private applyDatabasePriority(databases: string[]): string[] {
    const priorityOrder = [
      'orders',      // Highest priority - customer orders
      'staff',       // High priority - staff management
      'inventory',   // High priority - stock management
      'menu',        // Medium priority - menu items
      'settings',    // Medium priority - app settings
      'customers',   // Medium priority - customer data
      'payments',    // Medium priority - payment records
      'tables',      // Medium priority - table management
      'shifts',      // Low priority - shift management
      'reports'      // Low priority - reporting data
    ];
    
    const prioritized: string[] = [];
    
    // Add databases in priority order
    for (const dbName of priorityOrder) {
      if (databases.includes(dbName)) {
        prioritized.push(dbName);
      }
    }
    
    // Add remaining databases
    for (const dbName of databases) {
      if (!prioritized.includes(dbName)) {
        prioritized.push(dbName);
      }
    }
    
    return prioritized;
  }

  /**
   * Set up event listeners
   */
  private setupEventListeners(): void {
    // Listen for peer discovery events
    this.mobileP2PSync.onPeerDiscovered((peer) => {
      if (this.state.phase === 'discovering') {
        const updatedPeers = [...this.state.discoveredPeers, peer];
        this.handlePeersDiscovered(updatedPeers);
      }
    });

    // Listen for peer lost events
    this.mobileP2PSync.onPeerLost((peerId) => {
      this.updateState({
        discoveredPeers: this.state.discoveredPeers.filter(p => p.id !== peerId),
        connectedPeers: this.state.connectedPeers.filter(p => p.id !== peerId),
        lastActivity: new Date()
      });
    });

    // Listen for sync status updates
    this.mobileP2PSync.onSyncStatusUpdated((status) => {
      const updatedSyncStatus = [...this.state.syncStatus];
      const existingIndex = updatedSyncStatus.findIndex(s => s.peerId === status.peerId && s.dbName === status.dbName);
      
      if (existingIndex >= 0) {
        updatedSyncStatus[existingIndex] = status;
      } else {
        updatedSyncStatus.push(status);
      }

      this.updateState({
        syncStatus: updatedSyncStatus,
        lastActivity: new Date()
      });
    });
  }

  /**
   * Clean up event listeners
   */
  private cleanupEventListeners(): void {
    // Event listeners are added via callbacks, so they'll be cleaned up
    // when the MobileP2PSync instance is cleaned up
  }

  /**
   * Get auth token from storage or context
   */
  private getAuthToken(): string | null {
    // In a real app, this would get the token from secure storage
    // For now, return null to indicate no internet sync
    return null;
  }

  /**
   * Update state and notify callbacks
   */
  private updateState(updates: Partial<AutonomousSyncState>): void {
    this.state = { ...this.state, ...updates };
    this.stateCallbacks.forEach(callback => callback(this.state));
  }

  /**
   * Get human-readable status message
   */
  getStatusMessage(): string {
    switch (this.state.phase) {
      case 'initializing':
        return 'Initializing autonomous sync...';
      case 'discovering':
        return 'Discovering devices...';
      case 'connecting':
        return `Connecting to ${this.state.discoveredPeers.length} device(s)...`;
      case 'syncing':
        return `Syncing with ${this.state.connectedPeers.length} device(s)...`;
      case 'fallback':
        return 'Trying internet sync...';
      case 'error':
        return `Error: ${this.state.error}`;
      case 'complete':
        return `Connected to ${this.state.connectedPeers.length} device(s)`;
      default:
        return 'Unknown status';
    }
  }

  /**
   * Get connection summary
   */
  getConnectionSummary(): {
    localPeers: number;
    internetPeers: number;
    activeSyncs: number;
    lastActivity: Date;
  } {
    const localPeers = this.state.connectedPeers.filter(p => !(p as any).isInternetPeer).length;
    const internetPeers = this.state.connectedPeers.filter(p => (p as any).isInternetPeer).length;
    const activeSyncs = this.state.syncStatus.filter(s => s.status === 'active').length;

    return {
      localPeers,
      internetPeers,
      activeSyncs,
      lastActivity: this.state.lastActivity
    };
  }

  /**
   * Get error statistics
   */
  getErrorStatistics() {
    return this.errorHandler.getErrorStatistics();
  }

  /**
   * Clear error history
   */
  clearErrorHistory(): void {
    this.errorHandler.clearErrorHistory();
  }

  /**
   * Reset circuit breakers
   */
  resetCircuitBreakers(): void {
    this.errorHandler.resetAllCircuitBreakers();
  }

  /**
   * Get detailed diagnostics
   */
  getDiagnostics(): {
    state: AutonomousSyncState;
    config: AutonomousSyncConfig;
    errorStats: any;
    connectionSummary: any;
    systemHealth: {
      isRunning: boolean;
      timersActive: {
        discovery: boolean;
        fallback: boolean;
        retry: boolean;
      };
      lastActivity: Date;
      uptime: number;
    };
  } {
    const systemHealth = {
      isRunning: this.isRunning,
      timersActive: {
        discovery: this.discoveryTimer !== null,
        fallback: this.fallbackTimer !== null,
        retry: this.retryTimer !== null
      },
      lastActivity: this.state.lastActivity,
      uptime: this.state.lastActivity ? Date.now() - this.state.lastActivity.getTime() : 0
    };

    return {
      state: this.state,
      config: this.config,
      errorStats: this.errorHandler.getErrorStatistics(),
      connectionSummary: this.getConnectionSummary(),
      systemHealth
    };
  }
}