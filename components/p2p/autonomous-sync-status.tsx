"use client";

import { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { Separator } from '../ui/separator';
import { Progress } from '../ui/progress';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';
import { 
  Search, 
  Wifi, 
  WifiOff, 
  Globe, 
  RefreshCw, 
  AlertCircle, 
  CheckCircle, 
  Clock, 
  Monitor,
  Smartphone,
  ChevronDown,
  ChevronUp,
  Activity,
  Pause,
  Play
} from 'lucide-react';
import { useAutonomousSync } from '../../lib/hooks/use-autonomous-sync';
import { cn } from '../../lib/utils';

interface AutonomousSyncStatusProps {
  pouchDb: any;
  className?: string;
}

export function AutonomousSyncStatus({ pouchDb, className }: AutonomousSyncStatusProps) {
  const [isExpanded, setIsExpanded] = useState(false);
  const [showLogs, setShowLogs] = useState(false);
  
  const autonomousSync = useAutonomousSync(pouchDb);

  const getPhaseIcon = (phase: string) => {
    switch (phase) {
      case 'initializing':
        return <RefreshCw className="w-4 h-4 animate-spin" />;
      case 'discovering':
        return <Search className="w-4 h-4 animate-pulse" />;
      case 'connecting':
        return <Wifi className="w-4 h-4 animate-pulse" />;
      case 'syncing':
        return <Activity className="w-4 h-4 animate-pulse" />;
      case 'fallback':
        return <Globe className="w-4 h-4 animate-pulse" />;
      case 'complete':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-4 h-4 text-red-500" />;
      default:
        return <Clock className="w-4 h-4" />;
    }
  };

  const getPhaseColor = (phase: string) => {
    switch (phase) {
      case 'complete':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'error':
        return 'bg-red-100 text-red-800 border-red-200';
      case 'syncing':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'discovering':
      case 'connecting':
      case 'fallback':
        return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getSyncTypeColor = (syncType: string) => {
    switch (syncType) {
      case 'local':
        return 'bg-green-100 text-green-800';
      case 'internet':
        return 'bg-blue-100 text-blue-800';
      case 'both':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getSyncTypeIcon = (syncType: string) => {
    switch (syncType) {
      case 'local':
        return <Wifi className="w-3 h-3" />;
      case 'internet':
        return <Globe className="w-3 h-3" />;
      case 'both':
        return <Activity className="w-3 h-3" />;
      default:
        return <WifiOff className="w-3 h-3" />;
    }
  };

  const getConnectionProgress = () => {
    const { connected, total } = autonomousSync.connectionSummary;
    if (total === 0) return 0;
    return Math.round((connected / total) * 100);
  };

  const formatLastActivity = (date: Date) => {
    const now = new Date();
    const diff = now.getTime() - date.getTime();
    const seconds = Math.floor(diff / 1000);
    const minutes = Math.floor(seconds / 60);
    const hours = Math.floor(minutes / 60);

    if (seconds < 60) return `${seconds}s ago`;
    if (minutes < 60) return `${minutes}m ago`;
    if (hours < 24) return `${hours}h ago`;
    return date.toLocaleDateString();
  };

  if (!autonomousSync.isInitialized) {
    return (
      <Card className={cn('w-full', className)}>
        <CardContent className="pt-6">
          <div className="flex items-center justify-center space-x-2 text-gray-500">
            <RefreshCw className="w-4 h-4 animate-spin" />
            <span>Initializing autonomous sync...</span>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={cn('w-full', className)}>
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-2">
            <CardTitle className="text-lg">Autonomous Sync</CardTitle>
            <Badge 
              variant="outline" 
              className={cn('border', getPhaseColor(autonomousSync.autonomousState.phase))}
            >
              {getPhaseIcon(autonomousSync.autonomousState.phase)}
              <span className="ml-1 capitalize">{autonomousSync.autonomousState.phase}</span>
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => autonomousSync.retryConnection()}
              disabled={autonomousSync.autonomousState.phase === 'discovering'}
            >
              <RefreshCw className="w-4 h-4 mr-1" />
              Retry
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => {
                if (autonomousSync.isAutonomousActive) {
                  autonomousSync.stopAutonomousSync();
                } else {
                  autonomousSync.startAutonomousSync();
                }
              }}
            >
              {autonomousSync.isAutonomousActive ? (
                <>
                  <Pause className="w-4 h-4 mr-1" />
                  Stop
                </>
              ) : (
                <>
                  <Play className="w-4 h-4 mr-1" />
                  Start
                </>
              )}
            </Button>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Status Message */}
        <div className="text-center p-3 rounded-lg bg-gray-50">
          <p className="text-sm font-medium text-gray-700">
            {autonomousSync.statusMessage}
          </p>
        </div>

        {/* Connection Summary */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center">
            <div className="text-2xl font-bold text-blue-600">
              {autonomousSync.connectionSummary.connected}
            </div>
            <div className="text-xs text-gray-500">Connected</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-yellow-600">
              {autonomousSync.connectionSummary.connecting}
            </div>
            <div className="text-xs text-gray-500">Connecting</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-green-600">
              {autonomousSync.connectionSummary.activeSyncs}
            </div>
            <div className="text-xs text-gray-500">Active Syncs</div>
          </div>
          <div className="text-center">
            <div className="text-2xl font-bold text-gray-600">
              {autonomousSync.connectionSummary.total}
            </div>
            <div className="text-xs text-gray-500">Total Peers</div>
          </div>
        </div>

        {/* Connection Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Connection Progress</span>
            <span>{getConnectionProgress()}%</span>
          </div>
          <Progress value={getConnectionProgress()} className="h-2" />
        </div>

        {/* Sync Type */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Sync Type:</span>
          <Badge className={getSyncTypeColor(autonomousSync.fallbackSummary.activeSyncType)}>
            {getSyncTypeIcon(autonomousSync.fallbackSummary.activeSyncType)}
            <span className="ml-1 capitalize">{autonomousSync.fallbackSummary.activeSyncType}</span>
          </Badge>
        </div>

        {/* Fallback Summary */}
        <div className="grid grid-cols-3 gap-4 text-center">
          <div>
            <div className="flex items-center justify-center space-x-1">
              <Wifi className="w-4 h-4 text-green-500" />
              <span className="text-sm font-medium">{autonomousSync.fallbackSummary.localPeers}</span>
            </div>
            <div className="text-xs text-gray-500">Local</div>
          </div>
          <div>
            <div className="flex items-center justify-center space-x-1">
              <Globe className="w-4 h-4 text-blue-500" />
              <span className="text-sm font-medium">{autonomousSync.fallbackSummary.internetPeers}</span>
            </div>
            <div className="text-xs text-gray-500">Internet</div>
          </div>
          <div>
            <div className="flex items-center justify-center space-x-1">
              <Activity className="w-4 h-4 text-purple-500" />
              <span className="text-sm font-medium">{autonomousSync.fallbackSummary.totalPeers}</span>
            </div>
            <div className="text-xs text-gray-500">Total</div>
          </div>
        </div>

        {/* Expandable Details */}
        <Collapsible open={isExpanded} onOpenChange={setIsExpanded}>
          <CollapsibleTrigger asChild>
            <Button variant="ghost" className="w-full justify-between">
              <span>View Details</span>
              {isExpanded ? <ChevronUp className="w-4 h-4" /> : <ChevronDown className="w-4 h-4" />}
            </Button>
          </CollapsibleTrigger>
          
          <CollapsibleContent className="space-y-4">
            <Separator />
            
            {/* Discovered Peers */}
            <div>
              <h4 className="font-medium mb-2">Discovered Peers</h4>
              <div className="space-y-2">
                {autonomousSync.discoveredPeers.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-2">
                    No peers discovered yet
                  </div>
                ) : (
                  autonomousSync.discoveredPeers.map((peer) => (
                    <div key={peer.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center space-x-2">
                        <Monitor className="w-4 h-4 text-gray-500" />
                        <div>
                          <div className="font-medium text-sm">{peer.hostname}</div>
                          <div className="text-xs text-gray-500">{peer.ip}:{peer.port}</div>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {peer.platform}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Active Syncs */}
            <div>
              <h4 className="font-medium mb-2">Active Syncs</h4>
              <div className="space-y-2">
                {autonomousSync.syncStatuses.length === 0 ? (
                  <div className="text-sm text-gray-500 text-center py-2">
                    No active syncs
                  </div>
                ) : (
                  autonomousSync.syncStatuses.map((sync) => (
                    <div key={`${sync.peerId}-${sync.dbName}`} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                      <div className="flex items-center space-x-2">
                        <div className="w-2 h-2 rounded-full bg-green-500"></div>
                        <div>
                          <div className="font-medium text-sm">{sync.dbName}</div>
                          <div className="text-xs text-gray-500">
                            {sync.progress.docs_read} read, {sync.progress.docs_written} written
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="text-xs">
                        {sync.direction}
                      </Badge>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Manual Override Controls */}
            <div>
              <h4 className="font-medium mb-2">Manual Override</h4>
              <div className="flex space-x-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => autonomousSync.forceSwitchToLocal()}
                  disabled={autonomousSync.fallbackSummary.localPeers === 0}
                >
                  <Wifi className="w-4 h-4 mr-1" />
                  Force Local
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => autonomousSync.forceSwitchToInternet()}
                >
                  <Globe className="w-4 h-4 mr-1" />
                  Force Internet
                </Button>
              </div>
            </div>

            {/* Logs */}
            <div>
              <div className="flex justify-between items-center mb-2">
                <h4 className="font-medium">Logs</h4>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowLogs(!showLogs)}
                >
                  {showLogs ? 'Hide' : 'Show'} Logs
                </Button>
              </div>
              {showLogs && (
                <div className="bg-gray-900 text-gray-100 p-3 rounded text-xs font-mono max-h-40 overflow-y-auto">
                  {autonomousSync.logs.length === 0 ? (
                    <div className="text-gray-500">No logs yet</div>
                  ) : (
                    autonomousSync.logs.map((log, index) => (
                      <div key={index} className="mb-1">{log}</div>
                    ))
                  )}
                </div>
              )}
            </div>

            {/* Last Activity */}
            <div className="text-xs text-gray-500 text-center">
              Last activity: {formatLastActivity(autonomousSync.autonomousState.lastActivity)}
            </div>
          </CollapsibleContent>
        </Collapsible>
      </CardContent>
    </Card>
  );
}