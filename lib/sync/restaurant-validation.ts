/**
 * CRITICAL: Restaurant Validation Module
 * 
 * This module provides bulletproof restaurant ID validation to prevent
 * catastrophic cross-restaurant database sync scenarios.
 */

import { verifyToken } from '@/lib/auth/new-auth-service';

// Restaurant ID validation with cryptographic hash
export function createRestaurantHash(restaurantId: string): string {
  // Use a stable hash that can be verified across sessions
  // This prevents restaurant ID tampering or confusion
  const hash = btoa(restaurantId + '_BISTRO_SYNC_GUARD').slice(0, 16);
  return hash;
}

export function validateRestaurantHash(restaurantId: string, hash: string): boolean {
  return createRestaurantHash(restaurantId) === hash;
}

// Multi-layer restaurant ID validation
export interface RestaurantValidationResult {
  isValid: boolean;
  restaurantId: string | null;
  source: 'jwt' | 'localStorage' | 'cookie' | 'none';
  error?: string;
}

export function validateRestaurantContext(authToken?: string): RestaurantValidationResult {
  let jwtRestaurantId: string | null = null;
  let localStorageRestaurantId: string | null = null;
  let cookieRestaurantId: string | null = null;

  // 1. Validate JWT token restaurant ID
  if (authToken) {
    try {
      const decoded = verifyToken(authToken);
      if (decoded && decoded.restaurantId) {
        jwtRestaurantId = decoded.restaurantId;
      }
    } catch (error) {
      return {
        isValid: false,
        restaurantId: null,
        source: 'none',
        error: 'Invalid JWT token'
      };
    }
  }

  // 2. Check localStorage restaurant ID (if in browser)
  if (typeof window !== 'undefined' && window.localStorage) {
    try {
      const authData = localStorage.getItem('auth_data');
      if (authData) {
        const parsed = JSON.parse(authData);
        if (parsed.restaurantId) {
          localStorageRestaurantId = parsed.restaurantId;
        }
      }
    } catch (error) {
      console.warn('[RestaurantValidation] localStorage parsing error:', error);
    }
  }

  // 3. CRITICAL: Cross-validate restaurant IDs
  const restaurantIds = [jwtRestaurantId, localStorageRestaurantId].filter(Boolean);
  const uniqueRestaurantIds = [...new Set(restaurantIds)];

  if (uniqueRestaurantIds.length > 1) {
    // CATASTROPHIC: Multiple different restaurant IDs detected
    console.error('[RestaurantValidation] CRITICAL: Multiple restaurant IDs detected!', {
      jwt: jwtRestaurantId,
      localStorage: localStorageRestaurantId,
      cookie: cookieRestaurantId
    });
    
    return {
      isValid: false,
      restaurantId: null,
      source: 'none',
      error: 'Restaurant ID mismatch detected - potential cross-tenant access'
    };
  }

  if (uniqueRestaurantIds.length === 0) {
    return {
      isValid: false,
      restaurantId: null,
      source: 'none',
      error: 'No restaurant ID found'
    };
  }

  const validRestaurantId = uniqueRestaurantIds[0];
  const source = jwtRestaurantId ? 'jwt' : 'localStorage';

  return {
    isValid: true,
    restaurantId: validRestaurantId,
    source,
  };
}

// Database name validation with restaurant context
export function validateDatabaseName(dbName: string, restaurantId: string): boolean {
  if (!dbName || !restaurantId) {
    return false;
  }

  // Clean restaurant ID (same logic as db-instance.ts)
  const cleanRestaurantId = (id: string) => id.replace(/[^a-zA-Z0-9_-]/g, '');
  const expectedPrefix = `resto-${cleanRestaurantId(restaurantId)}`;
  
  // Database must start with the correct restaurant prefix
  return dbName.startsWith(expectedPrefix);
}

// Sync operation validation
export interface SyncValidationParams {
  authToken: string;
  targetDeviceId: string;
  requestedDbName: string;
  userRestaurantId?: string;
}

export function validateSyncOperation(params: SyncValidationParams): {
  isValid: boolean;
  error?: string;
  restaurantId?: string;
} {
  // 1. Validate restaurant context
  const restaurantValidation = validateRestaurantContext(params.authToken);
  if (!restaurantValidation.isValid) {
    return {
      isValid: false,
      error: `Restaurant validation failed: ${restaurantValidation.error}`
    };
  }

  const restaurantId = restaurantValidation.restaurantId!;

  // 2. Validate database name belongs to restaurant
  if (params.requestedDbName && !validateDatabaseName(params.requestedDbName, restaurantId)) {
    return {
      isValid: false,
      error: `Database ${params.requestedDbName} does not belong to restaurant ${restaurantId}`
    };
  }

  // 3. Additional validation if user restaurant ID is provided
  if (params.userRestaurantId && params.userRestaurantId !== restaurantId) {
    return {
      isValid: false,
      error: `User restaurant ID mismatch: expected ${restaurantId}, got ${params.userRestaurantId}`
    };
  }

  return {
    isValid: true,
    restaurantId
  };
}

// Emergency restaurant ID lock (prevents mid-session changes)
let lockedRestaurantId: string | null = null;
let lockTimestamp: number = 0;
const LOCK_DURATION = 30 * 60 * 1000; // 30 minutes

export function lockRestaurantContext(restaurantId: string): void {
  lockedRestaurantId = restaurantId;
  lockTimestamp = Date.now();
  console.log(`[RestaurantValidation] Restaurant context locked to: ${restaurantId}`);
}

export function validateRestaurantLock(restaurantId: string): boolean {
  // Check if lock is still valid
  if (Date.now() - lockTimestamp > LOCK_DURATION) {
    lockedRestaurantId = null;
    lockTimestamp = 0;
  }

  // If no lock exists, this is valid
  if (!lockedRestaurantId) {
    return true;
  }

  // Restaurant ID must match locked ID
  return lockedRestaurantId === restaurantId;
}

export function clearRestaurantLock(): void {
  lockedRestaurantId = null;
  lockTimestamp = 0;
  console.log('[RestaurantValidation] Restaurant context lock cleared');
}