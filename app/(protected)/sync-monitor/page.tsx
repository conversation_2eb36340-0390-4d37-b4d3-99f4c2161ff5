'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs';
import { ScrollArea } from '@/components/ui/scroll-area';
import { RefreshCw, Wifi, WifiOff, Cloud, CloudOff, Server, Smartphone, Monitor, Circle } from 'lucide-react';
import { useP2PSync } from '@/hooks/use-p2p-sync';
import { useMobileP2PSync } from '@/lib/hooks/use-mobile-p2p-sync';
import { isElectronApp, isMobileApp } from '@/lib/utils/environment';

interface InternetSyncStatus {
  enabled: boolean;
  serverUrl: string;
  registered: boolean;
}

export default function SyncMonitorPage() {
  const [internetSyncStatus, setInternetSyncStatus] = useState<InternetSyncStatus>({
    enabled: false,
    serverUrl: '',
    registered: false
  });
  const [isRefreshing, setIsRefreshing] = useState(false);
  const [lastRefresh, setLastRefresh] = useState<Date>(new Date());

  // Use appropriate sync hook based on platform
  const desktopSync = isElectronApp() ? useP2PSync() : null;
  const mobileSync = isMobileApp() ? useMobileP2PSync() : null;

  // Get data from the appropriate sync system
  const syncData = desktopSync || mobileSync || {
    isElectron: false,
    isInitialized: false,
    peers: [],
    syncStatuses: [],
    mdnsStatus: 'not_running' as const,
    logs: []
  };

  const platform = isElectronApp() ? 'desktop' : isMobileApp() ? 'mobile' : 'web';

  useEffect(() => {
    fetchInternetSyncStatus();
  }, []);

  const fetchInternetSyncStatus = async () => {
    try {
      if (isElectronApp() && window && (window as any).electronAPI) {
        const status = await (window as any).electronAPI.invoke('p2p-get-internet-sync-status');
        setInternetSyncStatus(status);
      }
    } catch (error) {
      console.error('Failed to fetch internet sync status:', error);
    }
  };

  const handleRefresh = async () => {
    setIsRefreshing(true);
    await fetchInternetSyncStatus();
    setLastRefresh(new Date());
    setIsRefreshing(false);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'running':
        return <Circle className="h-3 w-3 fill-green-500 text-green-500" />;
      case 'not_running':
        return <Circle className="h-3 w-3 fill-yellow-500 text-yellow-500" />;
      case 'error':
        return <Circle className="h-3 w-3 fill-red-500 text-red-500" />;
      default:
        return <Circle className="h-3 w-3 fill-gray-500 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active':
      case 'running':
        return 'bg-green-500';
      case 'paused':
      case 'not_running':
        return 'bg-yellow-500';
      case 'error':
        return 'bg-red-500';
      case 'complete':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Sync Monitor</h1>
          <p className="text-muted-foreground">
            Monitor local P2P and internet sync status and logs
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline" className="capitalize">
            {platform}
          </Badge>
          <Button
            variant="outline"
            size="sm"
            onClick={handleRefresh}
            disabled={isRefreshing}
          >
            <RefreshCw className={`h-4 w-4 mr-2 ${isRefreshing ? 'animate-spin' : ''}`} />
            Refresh
          </Button>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Local Sync Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Wifi className="h-4 w-4" />
              Local Sync
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {getStatusIcon(syncData.mdnsStatus)}
              <span className="text-sm capitalize">{syncData.mdnsStatus.replace('_', ' ')}</span>
            </div>
          </CardContent>
        </Card>

        {/* Internet Sync Status */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Cloud className="h-4 w-4" />
              Internet Sync
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              {internetSyncStatus.enabled ? (
                internetSyncStatus.registered ? (
                  <Circle className="h-3 w-3 fill-green-500 text-green-500" />
                ) : (
                  <Circle className="h-3 w-3 fill-yellow-500 text-yellow-500" />
                )
              ) : (
                <Circle className="h-3 w-3 fill-gray-500 text-gray-500" />
              )}
              <span className="text-sm">
                {internetSyncStatus.enabled 
                  ? internetSyncStatus.registered 
                    ? 'Connected' 
                    : 'Connecting...'
                  : 'Disabled'
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Discovered Peers */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <Server className="h-4 w-4" />
              Peers
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{syncData.peers.length}</div>
            <p className="text-xs text-muted-foreground">devices discovered</p>
          </CardContent>
        </Card>

        {/* Active Syncs */}
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Active Syncs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {syncData.syncStatuses.filter(s => s.status === 'active').length}
            </div>
            <p className="text-xs text-muted-foreground">
              of {syncData.syncStatuses.length} total
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="peers" className="space-y-4">
        <TabsList>
          <TabsTrigger value="peers">Discovered Peers</TabsTrigger>
          <TabsTrigger value="syncs">Sync Status</TabsTrigger>
          <TabsTrigger value="logs">System Logs</TabsTrigger>
          <TabsTrigger value="config">Configuration</TabsTrigger>
        </TabsList>

        <TabsContent value="peers" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Discovered Peers</CardTitle>
              <CardDescription>
                Devices discovered via local network (mDNS) and internet server
              </CardDescription>
            </CardHeader>
            <CardContent>
              {syncData.peers.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <WifiOff className="h-8 w-8 mx-auto mb-2" />
                  <p>No peers discovered</p>
                  <p className="text-xs">Make sure devices are on the same network</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {syncData.peers.map((peer) => (
                    <div
                      key={peer.id}
                      className="flex items-center justify-between p-3 border rounded-lg"
                    >
                      <div className="flex items-center gap-3">
                        {peer.platform === 'desktop' ? (
                          <Monitor className="h-5 w-5 text-blue-500" />
                        ) : (
                          <Smartphone className="h-5 w-5 text-green-500" />
                        )}
                        <div>
                          <div className="font-medium">{peer.hostname}</div>
                          <div className="text-sm text-muted-foreground">
                            {peer.ip}:{peer.port} • {peer.platform}
                          </div>
                        </div>
                      </div>
                      <Badge variant="outline" className="capitalize">
                        {(peer as any).isInternetPeer ? 'Internet' : 'Local'}
                      </Badge>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="syncs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Sync Status</CardTitle>
              <CardDescription>
                Current database synchronization status
              </CardDescription>
            </CardHeader>
            <CardContent>
              {syncData.syncStatuses.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <CloudOff className="h-8 w-8 mx-auto mb-2" />
                  <p>No active syncs</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {syncData.syncStatuses.map((sync, index) => {
                    const peer = syncData.peers.find(p => p.id === sync.peerId);
                    return (
                      <div
                        key={`${sync.peerId}-${sync.dbName}-${index}`}
                        className="flex items-center justify-between p-3 border rounded-lg"
                      >
                        <div className="flex items-center gap-3">
                          <div className={`w-2 h-2 rounded-full ${getStatusColor(sync.status)}`} />
                          <div>
                            <div className="font-medium">{sync.dbName}</div>
                            <div className="text-sm text-muted-foreground">
                              {peer?.hostname || sync.peerId.substring(0, 8)} • {sync.direction}
                            </div>
                          </div>
                        </div>
                        <div className="text-right">
                          <Badge 
                            variant={sync.status === 'active' ? 'default' : 'secondary'}
                            className="capitalize"
                          >
                            {sync.status}
                          </Badge>
                          {sync.progress && (
                            <div className="text-xs text-muted-foreground mt-1">
                              {sync.progress.docs_read || 0} read • {sync.progress.docs_written || 0} written
                            </div>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="logs" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>System Logs</CardTitle>
              <CardDescription>
                Real-time P2P sync system logs and events
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-96 w-full">
                {syncData.logs.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <p>No logs available</p>
                    <p className="text-xs">Logs will appear as sync events occur</p>
                  </div>
                ) : (
                  <div className="space-y-1">
                    {syncData.logs.map((log, index) => (
                      <div
                        key={index}
                        className="text-xs font-mono p-2 bg-muted rounded border-l-2 border-l-blue-500"
                      >
                        {log}
                      </div>
                    ))}
                  </div>
                )}
              </ScrollArea>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="config" className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Card>
              <CardHeader>
                <CardTitle>Local P2P Configuration</CardTitle>
                <CardDescription>
                  mDNS/Bonjour discovery settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Status:</span>
                  <Badge variant="outline" className="capitalize">
                    {syncData.mdnsStatus.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Platform:</span>
                  <span className="text-sm font-mono">{platform}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Initialized:</span>
                  <span className="text-sm">{syncData.isInitialized ? 'Yes' : 'No'}</span>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Internet Sync Configuration</CardTitle>
                <CardDescription>
                  Remote server sync settings
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-2">
                <div className="flex justify-between">
                  <span className="text-sm">Enabled:</span>
                  <Badge variant={internetSyncStatus.enabled ? 'default' : 'secondary'}>
                    {internetSyncStatus.enabled ? 'Yes' : 'No'}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-sm">Registered:</span>
                  <span className="text-sm">{internetSyncStatus.registered ? 'Yes' : 'No'}</span>
                </div>
                {internetSyncStatus.serverUrl && (
                  <div className="flex justify-between">
                    <span className="text-sm">Server:</span>
                    <span className="text-xs font-mono truncate max-w-32">
                      {internetSyncStatus.serverUrl}
                    </span>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>

          <Card>
            <CardHeader>
              <CardTitle>System Information</CardTitle>
              <CardDescription>
                Device and sync system details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="font-medium">Last Refresh:</span>
                  <div className="text-muted-foreground">
                    {lastRefresh.toLocaleTimeString()}
                  </div>
                </div>
                <div>
                  <span className="font-medium">Active Peers:</span>
                  <div className="text-muted-foreground">
                    {syncData.peers.length} discovered
                  </div>
                </div>
                <div>
                  <span className="font-medium">Running Syncs:</span>
                  <div className="text-muted-foreground">
                    {syncData.syncStatuses.filter(s => s.status === 'active').length} active
                  </div>
                </div>
                <div>
                  <span className="font-medium">Log Entries:</span>
                  <div className="text-muted-foreground">
                    {syncData.logs.length} messages
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}