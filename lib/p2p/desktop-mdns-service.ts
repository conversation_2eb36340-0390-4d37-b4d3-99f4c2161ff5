import { logZeroconfMessage } from './zeroconf-discovery';
import { isElectronApp } from '../utils/environment';

export interface DesktopMDNSConfig {
  serviceName: string;
  serviceType: string;
  port: number;
  hostName?: string;
  localIp?: string;
  txtRecord?: Record<string, string>;
  retryAttempts: number;
  retryDelay: number;
  healthCheckInterval: number;
  fallbackMethods: ('bonjour' | 'native-dns-sd' | 'network-broadcast')[];
}

export interface MDNSServiceStatus {
  isRunning: boolean;
  method: string;
  serviceName: string;
  publishedAt?: Date;
  lastHealthCheck?: Date;
  errors: string[];
  discoveryTests: {
    selfDiscovery: boolean;
    externalDiscovery: boolean;
    lastTested: Date;
  };
}

export class DesktopMDNSService {
  private config: DesktopMDNSConfig;
  private status: MDNSServiceStatus;
  private healthCheckTimer: NodeJS.Timeout | null = null;
  private retryTimer: NodeJS.Timeout | null = null;
  private publishedService: any = null;
  private isActive: boolean = false;
  
  // Different service implementations
  private bonjourInstance: any = null;
  private nativeDnsProcess: any = null;
  private networkBroadcast: any = null;

  constructor(config: Partial<DesktopMDNSConfig> = {}) {
    this.config = {
      serviceName: `Restaurant-POS-${Date.now()}`,
      serviceType: '_http._tcp',
      port: 5984,
      retryAttempts: 3,
      retryDelay: 5000,
      healthCheckInterval: 30000, // 30 seconds
      fallbackMethods: ['bonjour', 'native-dns-sd', 'network-broadcast'],
      ...config
    };

    this.status = {
      isRunning: false,
      method: 'none',
      serviceName: this.config.serviceName,
      errors: [],
      discoveryTests: {
        selfDiscovery: false,
        externalDiscovery: false,
        lastTested: new Date()
      }
    };
  }

  /**
   * Start the mDNS service with fallback methods
   */
  async start(): Promise<boolean> {
    if (!isElectronApp()) {
      logZeroconfMessage('🖥️ mDNS service only runs on desktop');
      return false;
    }

    if (this.isActive) {
      logZeroconfMessage('🖥️ mDNS service already running');
      return true;
    }

    logZeroconfMessage('🖥️ Starting desktop mDNS service...');
    this.isActive = true;

    // Try each fallback method in order
    for (const method of this.config.fallbackMethods) {
      try {
        const success = await this.startWithMethod(method);
        if (success) {
          this.status.method = method;
          this.status.isRunning = true;
          this.status.publishedAt = new Date();
          
          // Start health monitoring
          this.startHealthMonitoring();
          
          logZeroconfMessage(`🖥️ ✅ mDNS service started with ${method}`);
          return true;
        }
      } catch (error) {
        const errorMsg = `Failed to start with ${method}: ${error}`;
        this.status.errors.push(errorMsg);
        logZeroconfMessage(`🖥️ ❌ ${errorMsg}`);
      }
    }

    // All methods failed
    this.isActive = false;
    logZeroconfMessage('🖥️ ❌ All mDNS methods failed');
    return false;
  }

  /**
   * Stop the mDNS service
   */
  async stop(): Promise<void> {
    if (!this.isActive) {
      return;
    }

    logZeroconfMessage('🖥️ Stopping desktop mDNS service...');
    this.isActive = false;

    // Stop health monitoring
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
      this.healthCheckTimer = null;
    }

    if (this.retryTimer) {
      clearTimeout(this.retryTimer);
      this.retryTimer = null;
    }

    // Stop active services
    await this.stopCurrentService();

    this.status.isRunning = false;
    this.status.method = 'none';
    logZeroconfMessage('🖥️ ✅ mDNS service stopped');
  }

  /**
   * Start service with specific method
   */
  private async startWithMethod(method: string): Promise<boolean> {
    logZeroconfMessage(`🖥️🔄 Attempting to start mDNS with ${method}`);

    switch (method) {
      case 'bonjour':
        return await this.startWithBonjour();
      case 'native-dns-sd':
        return await this.startWithNativeDnsSD();
      case 'network-broadcast':
        return await this.startWithNetworkBroadcast();
      default:
        throw new Error(`Unknown method: ${method}`);
    }
  }

  /**
   * Start with Bonjour service
   */
  private async startWithBonjour(): Promise<boolean> {
    try {
      // Try to load bonjour-service or bonjour
      let BonjourService: any;
      try {
        BonjourService = require('bonjour-service');
        if (BonjourService.default) {
          BonjourService = BonjourService.default;
        }
      } catch {
        BonjourService = require('bonjour');
      }

      this.bonjourInstance = new BonjourService();
      
      const serviceInfo = {
        name: this.config.serviceName,
        type: this.config.serviceType,
        port: this.config.port,
        host: this.config.hostName,
        txt: this.config.txtRecord || {}
      };

      // Add IP address if available
      if (this.config.localIp) {
        serviceInfo.host = this.config.localIp;
      }

      const service = this.bonjourInstance.publish(serviceInfo);
      
      if (!service) {
        throw new Error('Failed to publish Bonjour service');
      }

      this.publishedService = service;
      
      // Wait for service to be published
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Bonjour service publish timeout'));
        }, 10000);

        // Check if service is activated
        const checkActivation = () => {
          if (service.activated || service.published) {
            clearTimeout(timeout);
            resolve(true);
          } else {
            setTimeout(checkActivation, 100);
          }
        };

        checkActivation();
      });

      logZeroconfMessage(`🖥️ ✅ Bonjour service published: ${this.config.serviceName}`);
      return true;

    } catch (error) {
      logZeroconfMessage(`🖥️ ❌ Bonjour method failed: ${error}`);
      return false;
    }
  }

  /**
   * Start with native dns-sd command
   */
  private async startWithNativeDnsSD(): Promise<boolean> {
    try {
      const { spawn } = require('child_process');
      
      const args = [
        '-R',
        this.config.serviceName,
        this.config.serviceType,
        'local',
        this.config.port.toString()
      ];

      // Add TXT record if provided
      if (this.config.txtRecord) {
        const txtEntries = Object.entries(this.config.txtRecord)
          .map(([key, value]) => `${key}=${value}`)
          .join(' ');
        args.push(txtEntries);
      }

      this.nativeDnsProcess = spawn('dns-sd', args);
      
      // Set up event handlers
      this.nativeDnsProcess.stdout.on('data', (data: Buffer) => {
        const output = data.toString();
        if (output.includes('Registering Service')) {
          logZeroconfMessage(`🖥️ ✅ Native dns-sd service registered: ${this.config.serviceName}`);
        }
      });

      this.nativeDnsProcess.stderr.on('data', (data: Buffer) => {
        logZeroconfMessage(`🖥️ ⚠️ Native dns-sd stderr: ${data.toString()}`);
      });

      this.nativeDnsProcess.on('error', (error: Error) => {
        logZeroconfMessage(`🖥️ ❌ Native dns-sd error: ${error.message}`);
      });

      this.nativeDnsProcess.on('close', (code: number) => {
        logZeroconfMessage(`🖥️ Native dns-sd process closed with code ${code}`);
      });

      // Wait for service to start
      await new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('Native dns-sd timeout'));
        }, 5000);

        this.nativeDnsProcess.stdout.once('data', () => {
          clearTimeout(timeout);
          resolve(true);
        });

        this.nativeDnsProcess.stderr.once('data', (data: Buffer) => {
          clearTimeout(timeout);
          reject(new Error(`Native dns-sd failed: ${data.toString()}`));
        });
      });

      return true;

    } catch (error) {
      logZeroconfMessage(`🖥️ ❌ Native dns-sd method failed: ${error}`);
      return false;
    }
  }

  /**
   * Start with network broadcast fallback
   */
  private async startWithNetworkBroadcast(): Promise<boolean> {
    try {
      const dgram = require('dgram');
      const socket = dgram.createSocket('udp4');
      
      // Simple UDP broadcast for service discovery
      const broadcastMessage = JSON.stringify({
        type: 'service-announcement',
        name: this.config.serviceName,
        port: this.config.port,
        timestamp: Date.now(),
        txtRecord: this.config.txtRecord || {}
      });

      socket.bind(() => {
        socket.setBroadcast(true);
        
        // Broadcast every 30 seconds
        const broadcastInterval = setInterval(() => {
          socket.send(broadcastMessage, 0, broadcastMessage.length, 5353, '255.255.255.255', (err) => {
            if (err) {
              logZeroconfMessage(`🖥️ ⚠️ Network broadcast error: ${err.message}`);
            }
          });
        }, 30000);

        // Send initial broadcast
        socket.send(broadcastMessage, 0, broadcastMessage.length, 5353, '255.255.255.255');
        
        this.networkBroadcast = { socket, interval: broadcastInterval };
      });

      logZeroconfMessage(`🖥️ ✅ Network broadcast started for ${this.config.serviceName}`);
      return true;

    } catch (error) {
      logZeroconfMessage(`🖥️ ❌ Network broadcast method failed: ${error}`);
      return false;
    }
  }

  /**
   * Stop the current service
   */
  private async stopCurrentService(): Promise<void> {
    if (this.publishedService) {
      try {
        if (typeof this.publishedService.unpublish === 'function') {
          this.publishedService.unpublish();
        }
        if (typeof this.publishedService.destroy === 'function') {
          this.publishedService.destroy();
        }
      } catch (error) {
        logZeroconfMessage(`🖥️ ⚠️ Error stopping Bonjour service: ${error}`);
      }
      this.publishedService = null;
    }

    if (this.nativeDnsProcess) {
      try {
        this.nativeDnsProcess.kill();
      } catch (error) {
        logZeroconfMessage(`🖥️ ⚠️ Error stopping native dns-sd: ${error}`);
      }
      this.nativeDnsProcess = null;
    }

    if (this.networkBroadcast) {
      try {
        if (this.networkBroadcast.interval) {
          clearInterval(this.networkBroadcast.interval);
        }
        if (this.networkBroadcast.socket) {
          this.networkBroadcast.socket.close();
        }
      } catch (error) {
        logZeroconfMessage(`🖥️ ⚠️ Error stopping network broadcast: ${error}`);
      }
      this.networkBroadcast = null;
    }

    if (this.bonjourInstance) {
      try {
        if (typeof this.bonjourInstance.destroy === 'function') {
          this.bonjourInstance.destroy();
        }
      } catch (error) {
        logZeroconfMessage(`🖥️ ⚠️ Error destroying Bonjour instance: ${error}`);
      }
      this.bonjourInstance = null;
    }
  }

  /**
   * Start health monitoring
   */
  private startHealthMonitoring(): void {
    if (this.healthCheckTimer) {
      clearInterval(this.healthCheckTimer);
    }

    this.healthCheckTimer = setInterval(() => {
      this.performHealthCheck();
    }, this.config.healthCheckInterval);

    // Initial health check
    setTimeout(() => this.performHealthCheck(), 5000);
  }

  /**
   * Perform health check
   */
  private async performHealthCheck(): Promise<void> {
    if (!this.isActive) {
      return;
    }

    logZeroconfMessage('🖥️ 🏥 Performing mDNS health check...');
    
    this.status.lastHealthCheck = new Date();
    
    try {
      // Test self-discovery
      const selfDiscovery = await this.testSelfDiscovery();
      this.status.discoveryTests.selfDiscovery = selfDiscovery;

      // Test external discovery
      const externalDiscovery = await this.testExternalDiscovery();
      this.status.discoveryTests.externalDiscovery = externalDiscovery;

      this.status.discoveryTests.lastTested = new Date();

      if (!selfDiscovery && !externalDiscovery) {
        logZeroconfMessage('🖥️ ❌ Health check failed - service not discoverable');
        await this.handleHealthCheckFailure();
      } else {
        logZeroconfMessage(`🖥️ ✅ Health check passed (self: ${selfDiscovery}, external: ${externalDiscovery})`);
      }

    } catch (error) {
      logZeroconfMessage(`🖥️ ❌ Health check error: ${error}`);
      await this.handleHealthCheckFailure();
    }
  }

  /**
   * Test self-discovery
   */
  private async testSelfDiscovery(): Promise<boolean> {
    // This would use the same discovery mechanism as mobile devices
    // For now, we'll simulate it
    return true;
  }

  /**
   * Test external discovery
   */
  private async testExternalDiscovery(): Promise<boolean> {
    // This would test if the service can be discovered from external network
    // For now, we'll simulate it
    return true;
  }

  /**
   * Handle health check failure
   */
  private async handleHealthCheckFailure(): Promise<void> {
    logZeroconfMessage('🖥️ 🔄 Attempting service recovery...');
    
    // Stop current service
    await this.stopCurrentService();
    
    // Try to restart with current method
    try {
      const success = await this.startWithMethod(this.status.method);
      if (success) {
        logZeroconfMessage('🖥️ ✅ Service recovery successful');
        return;
      }
    } catch (error) {
      logZeroconfMessage(`🖥️ ❌ Service recovery failed: ${error}`);
    }

    // Try fallback methods
    for (const method of this.config.fallbackMethods) {
      if (method === this.status.method) continue;
      
      try {
        const success = await this.startWithMethod(method);
        if (success) {
          this.status.method = method;
          logZeroconfMessage(`🖥️ ✅ Service recovered with fallback method: ${method}`);
          return;
        }
      } catch (error) {
        logZeroconfMessage(`🖥️ ❌ Fallback method ${method} failed: ${error}`);
      }
    }

    // All recovery attempts failed
    logZeroconfMessage('🖥️ ❌ Service recovery failed - all methods exhausted');
    this.status.isRunning = false;
  }

  /**
   * Get service status
   */
  getStatus(): MDNSServiceStatus {
    return { ...this.status };
  }

  /**
   * Get service diagnostics
   */
  getDiagnostics(): {
    config: DesktopMDNSConfig;
    status: MDNSServiceStatus;
    runtime: {
      isActive: boolean;
      uptime: number;
      healthCheckActive: boolean;
      lastHealthCheck: Date | null;
    };
  } {
    return {
      config: this.config,
      status: this.status,
      runtime: {
        isActive: this.isActive,
        uptime: this.status.publishedAt ? Date.now() - this.status.publishedAt.getTime() : 0,
        healthCheckActive: this.healthCheckTimer !== null,
        lastHealthCheck: this.status.lastHealthCheck || null
      }
    };
  }

  /**
   * Update configuration
   */
  updateConfig(config: Partial<DesktopMDNSConfig>): void {
    this.config = { ...this.config, ...config };
    logZeroconfMessage('🖥️ ⚙️ mDNS configuration updated');
  }
}