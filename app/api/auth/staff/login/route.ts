import { NextRequest, NextResponse } from 'next/server';
import { getMongoUserByUsername } from '@/lib/auth/mongo-auth-ops';
import { verifyPassword } from '@/lib/auth/new-auth-service';
import { generateToken } from '@/lib/auth/new-auth-service';
import { User as JwtUser } from '@/lib/auth/new-auth-service';
import { getStaffMember } from '@/lib/db/v4/operations/per-staff-ops';
import { initializeV4Database } from '@/lib/db/v4';
import { getAllStaff } from '@/lib/db/v4/operations/per-staff-ops';

export async function POST(req: NextRequest) {
  let staffUser: any = null;
  
  try {
    console.log('[API Staff Login] Received POST request');
    const body = await req.json();
    console.log('[API Staff Login] Request body:', body);
    const { username, password } = body;

    if (!username || !password) {
      console.warn('[API Staff Login] Missing username or password:', { username, password });
      return NextResponse.json(
        { error: 'Username and password are required' },
        { status: 400 }
      );
    }

    // 1. Fetch user from MongoDB by username (no restaurantId) - THIS MUST NOT FAIL
    try {
      console.log('[API Staff Login] Fetching user from MongoDB by username:', username);
      staffUser = await getMongoUserByUsername(username);

      if (staffUser) {
        console.log('[API Staff Login] MongoDB user found:', {
          _id: staffUser._id,
          name: staffUser.name,
          username: staffUser.username,
          role: staffUser.role,
          restaurantId: staffUser.restaurantId
        });
      } else {
        console.log('[API Staff Login] No user found with username:', username);
        return NextResponse.json({ error: 'Invalid credentials - user not found' }, { status: 401 });
      }

      // 2. Verify password - THIS MUST NOT FAIL
      console.log('[API Staff Login] Verifying password for user:', username);
      const isValidPassword = await verifyPassword(password, staffUser.password);
      console.log('[API Staff Login] Password valid:', isValidPassword);

      if (!isValidPassword) {
        console.warn('[API Staff Login] Invalid credentials - password incorrect for username:', username);
        return NextResponse.json({ error: 'Invalid credentials - password incorrect' }, { status: 401 });
      }
    } catch (authError) {
      console.error('[API Staff Login] MongoDB authentication failed:', authError);
      return NextResponse.json({ error: 'Authentication failed' }, { status: 401 });
    }

    // 3. Try to fetch permissions from PouchDB, but don't fail login if it fails
    let actualPermissions = null;
    
    if (staffUser.restaurantId) {
      try {
        console.log('[API Staff Login] Attempting to fetch permissions from PouchDB');
        
        // Initialize V4 database for the restaurant
        await initializeV4Database(staffUser.restaurantId);
        
        // Get ALL staff members and find the one with matching userId
        const allStaff = await getAllStaff();
        const staffMember = allStaff.find(staff => staff.userId === staffUser._id);
        
        if (staffMember?.permissions) {
          actualPermissions = staffMember.permissions;
          console.log('[API Staff Login] ✅ Permissions loaded successfully');
        } else {
          console.log('[API Staff Login] ⚠️ Staff member found but no permissions');
        }
      } catch (error) {
        console.warn('[API Staff Login] ⚠️ Could not fetch permissions, proceeding without them:', error instanceof Error ? error.message : error);
        // Continue with login even if permissions fail - permissions can be loaded later
      }
    }

    // 4. Generate JWT and return success - THIS MUST WORK
    try {
      const userForToken: JwtUser = {
        id: staffUser._id,
        name: staffUser.name,
        email: staffUser.email,
        username: staffUser.username,
        role: staffUser.role,
        restaurantId: staffUser.restaurantId,
        permissions: actualPermissions, // Use actual permissions from PouchDB (may be null)
        metadata: staffUser.metadata,
      };
      console.log('[API Staff Login] User object for JWT:', {
        ...userForToken,
        permissions: actualPermissions ? 'included' : 'none'
      });

      const token = generateToken(userForToken);
      console.log('[API Staff Login] JWT generated successfully for user:', username);

      return NextResponse.json({
        message: 'Staff login successful',
        token,
        user: {
          id: staffUser._id,
          name: staffUser.name,
          username: staffUser.username,
          role: staffUser.role,
          restaurantId: staffUser.restaurantId
        }
      }, {
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });
    } catch (tokenError) {
      console.error('[API Staff Login] JWT generation failed:', tokenError);
      return NextResponse.json({ error: 'Token generation failed' }, { status: 500 });
    }

  } catch (error) {
    console.error('[API Staff Login] Unexpected error during staff login:', error);
    
    // Check if this is a database/PouchDB related error vs auth error
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    const isDatabaseError = errorMessage.includes('Database') || 
                           errorMessage.includes('PouchDB') || 
                           errorMessage.includes('not initialized') ||
                           errorMessage.includes('IndexedDB');
    
    if (isDatabaseError) {
      console.warn('[API Staff Login] Database error occurred, but user auth may still be valid');
      return NextResponse.json({ 
        error: 'Database connection issue. Please try again or contact support.',
        type: 'database_error'
      }, { 
        status: 503, // Service Unavailable instead of 500
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        }
      });
    }
    
    return NextResponse.json({ error: `Login failed: ${errorMessage}` }, { 
      status: 500,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      }
    });
  }
}

// Handle CORS preflight requests
export async function OPTIONS() {
  return new NextResponse(null, {
    status: 204,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      'Access-Control-Max-Age': '86400',
    },
  });
}