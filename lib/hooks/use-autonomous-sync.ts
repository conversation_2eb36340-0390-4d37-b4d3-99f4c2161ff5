import { useState, useEffect, useCallback } from 'react';
import { MobileP2PSync } from '../p2p/mobile-p2p-sync';
import { AutonomousSyncDaemon, AutonomousSyncConfig, AutonomousSyncState } from '../p2p/autonomous-sync-daemon';
import { ConnectionManager } from '../p2p/connection-manager';
import { FallbackCoordinator } from '../p2p/fallback-coordinator';
import { PeerInfo, onZeroconfLog, logZeroconfMessage } from '../p2p/zeroconf-discovery';
import { SyncStatus } from '../../types/p2p-sync';
import { isMobileApp } from '../utils/environment';

// Global instances for singleton pattern
let mobileP2PSyncInstance: MobileP2PSync | null = null;
let autonomousSyncDaemonInstance: AutonomousSyncDaemon | null = null;
let connectionManagerInstance: ConnectionManager | null = null;
let fallbackCoordinatorInstance: FallbackCoordinator | null = null;

export interface AutonomousSyncHookState {
  isInitialized: boolean;
  isAutonomousActive: boolean;
  discoveredPeers: PeerInfo[];
  connectedPeers: PeerInfo[];
  syncStatuses: SyncStatus[];
  autonomousState: AutonomousSyncState;
  connectionSummary: {
    total: number;
    connected: number;
    connecting: number;
    disconnected: number;
    error: number;
    activeSyncs: number;
  };
  fallbackSummary: {
    phase: string;
    activeSyncType: string;
    localPeers: number;
    internetPeers: number;
    totalPeers: number;
    lastFallbackTime?: Date;
  };
  error?: Error;
  logs: string[];
  statusMessage: string;
}

/**
 * Hook for autonomous P2P sync - automatically discovers and connects to peers
 * 
 * This hook provides:
 * - Automatic device discovery with timeout
 * - Auto-connection to discovered peers
 * - Intelligent local/internet fallback
 * - Multi-master sync with all available peers
 * - Connection management and health monitoring
 * - Status tracking and error handling
 */
export function useAutonomousSync(
  pouchDb: any, 
  config: Partial<AutonomousSyncConfig> = {}
): AutonomousSyncHookState & {
  // Actions
  startAutonomousSync: () => Promise<void>;
  stopAutonomousSync: () => Promise<void>;
  retryConnection: () => Promise<void>;
  configureAutonomousSync: (config: Partial<AutonomousSyncConfig>) => void;
  
  // Manual overrides
  forceSwitchToLocal: () => void;
  forceSwitchToInternet: () => void;
  
  // Status methods
  getStatusMessage: () => string;
  getConnectionSummary: () => any;
  getFallbackSummary: () => any;
} {
  const [state, setState] = useState<AutonomousSyncHookState>({
    isInitialized: false,
    isAutonomousActive: false,
    discoveredPeers: [],
    connectedPeers: [],
    syncStatuses: [],
    autonomousState: {
      phase: 'initializing',
      discoveredPeers: [],
      connectedPeers: [],
      syncStatus: [],
      lastActivity: new Date()
    },
    connectionSummary: {
      total: 0,
      connected: 0,
      connecting: 0,
      disconnected: 0,
      error: 0,
      activeSyncs: 0
    },
    fallbackSummary: {
      phase: 'local_discovery',
      activeSyncType: 'none',
      localPeers: 0,
      internetPeers: 0,
      totalPeers: 0
    },
    logs: [],
    statusMessage: 'Initializing...'
  });

  // Initialize all components
  useEffect(() => {
    if (!isMobileApp()) {
      return;
    }

    async function initializeAutonomousSync() {
      try {
        logZeroconfMessage('🤖 Initializing autonomous P2P sync system...');

        // Add log listener
        const logListener = (log: string) => {
          setState(prev => ({
            ...prev,
            logs: [...prev.logs.slice(-99), log] // Keep last 100 logs
          }));
        };
        onZeroconfLog(logListener);

        // Create MobileP2PSync instance
        if (!mobileP2PSyncInstance) {
          mobileP2PSyncInstance = new MobileP2PSync();
        }

        // Initialize P2P sync
        const initialized = await mobileP2PSyncInstance.initialize(pouchDb);
        if (!initialized) {
          throw new Error('Failed to initialize mobile P2P sync');
        }

        // Create connection manager
        if (!connectionManagerInstance) {
          connectionManagerInstance = new ConnectionManager({
            maxConcurrentConnections: 5,
            connectionTimeout: 30000,
            healthCheckInterval: 60000,
            retryAttempts: 3,
            retryDelay: 5000
          });
        }

        // Create fallback coordinator
        if (!fallbackCoordinatorInstance) {
          fallbackCoordinatorInstance = new FallbackCoordinator(
            mobileP2PSyncInstance,
            connectionManagerInstance,
            {
              enabled: true,
              localDiscoveryTimeout: 60000,
              internetFallbackDelay: 5000,
              preferLocalOverInternet: true,
              syncStrategy: 'local_preferred'
            }
          );
        }

        // Create autonomous sync daemon
        if (!autonomousSyncDaemonInstance) {
          autonomousSyncDaemonInstance = new AutonomousSyncDaemon(
            mobileP2PSyncInstance,
            {
              enabled: true,
              discoveryTimeout: 60000,
              fallbackDelay: 5000,
              autoSyncDatabases: ['orders', 'staff', 'inventory', 'settings'],
              preferLocalOverInternet: true,
              retryAttempts: 3,
              retryDelay: 10000,
              ...config
            }
          );
        }

        // Set up event listeners
        setupEventListeners();

        // Start services
        connectionManagerInstance.start();
        fallbackCoordinatorInstance.start();

        setState(prev => ({
          ...prev,
          isInitialized: true,
          error: undefined,
          statusMessage: 'Ready for autonomous sync'
        }));

        logZeroconfMessage('🤖 ✅ Autonomous P2P sync system initialized');

      } catch (error) {
        console.error('Failed to initialize autonomous sync:', error);
        setState(prev => ({
          ...prev,
          error: error instanceof Error ? error : new Error(String(error)),
          statusMessage: `Error: ${error instanceof Error ? error.message : String(error)}`
        }));
      }
    }

    initializeAutonomousSync();

    return () => {
      // Cleanup
      if (autonomousSyncDaemonInstance) {
        autonomousSyncDaemonInstance.stop();
      }
      if (fallbackCoordinatorInstance) {
        fallbackCoordinatorInstance.stop();
      }
      if (connectionManagerInstance) {
        connectionManagerInstance.stop();
      }
    };
  }, [pouchDb]);

  // Set up event listeners
  const setupEventListeners = useCallback(() => {
    if (!mobileP2PSyncInstance || !autonomousSyncDaemonInstance || !connectionManagerInstance || !fallbackCoordinatorInstance) {
      return;
    }

    // Autonomous sync daemon state changes
    autonomousSyncDaemonInstance.onStateChanged((autonomousState) => {
      setState(prev => ({
        ...prev,
        autonomousState,
        discoveredPeers: autonomousState.discoveredPeers,
        connectedPeers: autonomousState.connectedPeers,
        syncStatuses: autonomousState.syncStatus,
        statusMessage: autonomousSyncDaemonInstance?.getStatusMessage() || 'Unknown'
      }));
    });

    // Connection manager events
    connectionManagerInstance.onConnectionStatusChange((peerId, connectionInfo) => {
      setState(prev => ({
        ...prev,
        connectionSummary: connectionManagerInstance?.getConnectionSummary() || prev.connectionSummary
      }));
    });

    // Fallback coordinator state changes
    fallbackCoordinatorInstance.onStateChanged((fallbackState) => {
      setState(prev => ({
        ...prev,
        fallbackSummary: fallbackCoordinatorInstance?.getSyncSummary() || prev.fallbackSummary
      }));
    });

    // P2P sync events
    mobileP2PSyncInstance.onPeerDiscovered((peer) => {
      logZeroconfMessage(`🤖 📡 Peer discovered: ${peer.hostname}`);
      
      // Notify fallback coordinator
      fallbackCoordinatorInstance?.onPeerDiscovered(peer);
    });

    mobileP2PSyncInstance.onPeerLost((peerId) => {
      logZeroconfMessage(`🤖 📡 Peer lost: ${peerId}`);
      
      // Notify fallback coordinator
      fallbackCoordinatorInstance?.onPeerLost(peerId);
    });

    mobileP2PSyncInstance.onSyncStatusUpdated((syncStatus) => {
      // Update connection manager with sync status
      connectionManagerInstance?.updateSyncStatus(syncStatus.peerId, syncStatus.dbName, syncStatus);
      
      setState(prev => ({
        ...prev,
        syncStatuses: [
          ...prev.syncStatuses.filter(s => !(s.peerId === syncStatus.peerId && s.dbName === syncStatus.dbName)),
          syncStatus
        ]
      }));
    });

  }, []);

  // Start autonomous sync
  const startAutonomousSync = useCallback(async () => {
    if (!autonomousSyncDaemonInstance) {
      throw new Error('Autonomous sync daemon not initialized');
    }

    logZeroconfMessage('🤖 🚀 Starting autonomous sync...');
    
    await autonomousSyncDaemonInstance.start();
    
    setState(prev => ({
      ...prev,
      isAutonomousActive: true,
      statusMessage: 'Autonomous sync active'
    }));

  }, []);

  // Stop autonomous sync
  const stopAutonomousSync = useCallback(async () => {
    if (!autonomousSyncDaemonInstance) {
      return;
    }

    logZeroconfMessage('🤖 🛑 Stopping autonomous sync...');
    
    await autonomousSyncDaemonInstance.stop();
    
    setState(prev => ({
      ...prev,
      isAutonomousActive: false,
      statusMessage: 'Autonomous sync stopped'
    }));

  }, []);

  // Retry connection
  const retryConnection = useCallback(async () => {
    if (!autonomousSyncDaemonInstance) {
      return;
    }

    logZeroconfMessage('🤖 🔄 Retrying connection...');
    
    await autonomousSyncDaemonInstance.stop();
    await autonomousSyncDaemonInstance.start();

  }, []);

  // Configure autonomous sync
  const configureAutonomousSync = useCallback((newConfig: Partial<AutonomousSyncConfig>) => {
    if (!autonomousSyncDaemonInstance) {
      return;
    }

    logZeroconfMessage(`🤖 ⚙️ Configuring autonomous sync: ${JSON.stringify(newConfig)}`);
    autonomousSyncDaemonInstance.configure(newConfig);

  }, []);

  // Force switch to local sync
  const forceSwitchToLocal = useCallback(() => {
    if (!fallbackCoordinatorInstance) {
      return;
    }

    logZeroconfMessage('🤖 🏠 Forcing switch to local sync');
    fallbackCoordinatorInstance.forceSwitchToLocal();

  }, []);

  // Force switch to internet sync
  const forceSwitchToInternet = useCallback(() => {
    if (!fallbackCoordinatorInstance) {
      return;
    }

    logZeroconfMessage('🤖 🌐 Forcing switch to internet sync');
    fallbackCoordinatorInstance.forceSwitchToInternet();

  }, []);

  // Status methods
  const getStatusMessage = useCallback(() => {
    return autonomousSyncDaemonInstance?.getStatusMessage() || 'Not initialized';
  }, []);

  const getConnectionSummary = useCallback(() => {
    return connectionManagerInstance?.getConnectionSummary() || state.connectionSummary;
  }, []);

  const getFallbackSummary = useCallback(() => {
    return fallbackCoordinatorInstance?.getSyncSummary() || state.fallbackSummary;
  }, []);

  return {
    ...state,
    
    // Actions
    startAutonomousSync,
    stopAutonomousSync,
    retryConnection,
    configureAutonomousSync,
    
    // Manual overrides
    forceSwitchToLocal,
    forceSwitchToInternet,
    
    // Status methods
    getStatusMessage,
    getConnectionSummary,
    getFallbackSummary
  };
}

/**
 * Auto-start autonomous sync hook
 * 
 * This hook automatically starts autonomous sync when the component mounts
 * and the sync system is initialized. Use this in your main app component.
 */
export function useAutoStartAutonomousSync(
  pouchDb: any,
  config: Partial<AutonomousSyncConfig> = {}
) {
  const autonomousSync = useAutonomousSync(pouchDb, config);

  // Auto-start autonomous sync when initialized
  useEffect(() => {
    if (autonomousSync.isInitialized && !autonomousSync.isAutonomousActive) {
      logZeroconfMessage('🤖 🚀 Auto-starting autonomous sync...');
      autonomousSync.startAutonomousSync().catch(error => {
        console.error('Failed to auto-start autonomous sync:', error);
      });
    }
  }, [autonomousSync.isInitialized, autonomousSync.isAutonomousActive]);

  return autonomousSync;
}